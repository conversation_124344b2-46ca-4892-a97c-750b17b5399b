<?php

namespace FDMC\FileServiceClient\Client;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;
use FDMC\FileServiceClient\Core\ConfigManager;
use FDMC\FileServiceClient\Exceptions\FileServiceException;

/**
 * Storage Manager
 * 用於管理 file-service 儲存相關操作的獨立類別
 */
class StorageManager
{
    private Client $httpClient;
    private string $baseUrl;
    private array $defaultHeaders;

    /**
     * 初始化儲存管理器
     * @param array $options 使用者選項，會與配置檔案合併
     */
    public function __construct(array $options = [])
    {
        // 使用配置管理器合併使用者選項與預設配置
        $mergedConfig = ConfigManager::getHttpClientConfig($options);

        $this->baseUrl = rtrim($mergedConfig['baseUrl'], '/');
        $this->defaultHeaders = $mergedConfig['headers'];

        $clientOptions = [
            'base_uri' => "{$this->baseUrl}/api",
            'timeout' => $mergedConfig['timeout'],
            'connect_timeout' => $mergedConfig['connect_timeout'],
            'headers' => $this->defaultHeaders,
            'verify' => $mergedConfig['verify'],
            'http_errors' => $mergedConfig['http_errors'],
        ];

        $this->httpClient = new Client($clientOptions);
    }

    /**
     * 發送 HTTP 請求
     * @param string $method 請求方法
     * @param string $endpoint 端點路徑
     * @param array $options 選項配置
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    private function sendRequest(string $method, string $endpoint, array $options = []): array
    {
        try {
            $response = $this->httpClient->request($method, $endpoint, $options);
            return $this->parseResponse($response);
        } catch (GuzzleException $e) {
            throw new FileServiceException('HTTP 請求失敗: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 解析回應
     * @param ResponseInterface $response 回應
     * @return array 解析後的資料
     * @throws FileServiceException 解析失敗時拋出例外
     */
    private function parseResponse(ResponseInterface $response): array
    {
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new FileServiceException('無法解析 JSON 回應: ' . json_last_error_msg());
        }

        if (!$data['success']) {
            throw new FileServiceException($data['message'] ?? '未知錯誤');
        }

        return $data;
    }

    /**
     * 設置自定義 HTTP 標頭
     * @param array $headers 自定義 HTTP 標頭
     */
    public function setHeaders(array $headers): void
    {
        $this->defaultHeaders = array_merge($this->defaultHeaders, $headers);
    }

    /**
     * 獲取當前設置的基礎 URL
     * @return string 基礎 URL
     */
    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    // ===== 儲存管理相關方法 =====

    /**
     * 列出所有儲存
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function listStorages(int $userId): array
    {
        $query = ['user_id' => $userId];

        return $this->sendRequest('GET', '/storages', [
            'query' => $query
        ]);
    }

    /**
     * 創建新儲存
     * @param int $userId 使用者 ID
     * @param array $storageData 儲存資料
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function createStorage(int $userId, array $storageData): array
    {
        $query = ['user_id' => $userId];

        return $this->sendRequest('POST', '/storages', [
            'query' => $query,
            'json' => $storageData
        ]);
    }

    /**
     * 獲取儲存詳情
     * @param string $storageId 儲存 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function getStorage(string $storageId, int $userId): array
    {
        $query = ['user_id' => $userId];

        return $this->sendRequest('GET', "/storages/{$storageId}", [
            'query' => $query
        ]);
    }

    /**
     * 更新儲存
     * @param string $storageId 儲存 ID
     * @param int $userId 使用者 ID
     * @param array $storageData 儲存資料
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function updateStorage(string $storageId, int $userId, array $storageData): array
    {
        $query = ['user_id' => $userId];

        return $this->sendRequest('PATCH', "/storages/{$storageId}", [
            'query' => $query,
            'json' => $storageData
        ]);
    }

    /**
     * 刪除儲存
     * @param string $storageId 儲存 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function deleteStorage(string $storageId, int $userId): array
    {
        $query = ['user_id' => $userId];

        return $this->sendRequest('DELETE', "/storages/{$storageId}", [
            'query' => $query
        ]);
    }

    /**
     * 建立儲存資料陣列的輔助方法
     * @param string $name 名稱
     * @param string $type 類型
     * @param string|null $host 主機
     * @param string|null $remark 備註
     * @param array|null $payload 負載
     * @return array 解析後的資料
     */
    public function buildStorageData(
        string $name,
        string $type,
        ?string $host = null,
        ?string $remark = null,
        ?array $payload = null
    ): array {
        $data = [
            'name' => $name,
            'type' => $type,
        ];

        if ($host !== null) {
            $data['host'] = $host;
        }

        if ($remark !== null) {
            $data['remark'] = $remark;
        }

        if ($payload !== null) {
            $data['payload'] = $payload;
        }

        return $data;
    }
}