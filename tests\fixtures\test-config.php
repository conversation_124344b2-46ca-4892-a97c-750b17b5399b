<?php

declare(strict_types=1);

// 測試用的配置檔案，不使用 env() 函數

return [
    'baseUrl' => 'http://localhost:8080',
    
    'http' => [
        'timeout' => 30,
        'connect_timeout' => 10,
        'read_timeout' => 60,
        'verify' => false,
        'http_errors' => false,
    ],
    
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'User-Agent' => 'FDMC-FileServiceClient/1.0',
    ],
    
    'upload' => [
        'timeout' => 120,
        'max_file_size' => 104857600, // 100MB
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'text/plain',
            'application/json',
            'application/xml',
            'application/zip',
            'application/x-zip-compressed',
        ],
    ],
    
    'download' => [
        'timeout' => 300,
        'stream_buffer_size' => 8192,
        'large_file_threshold' => 52428800, // 50MB
    ],
    
    'cache' => [
        'enabled' => false,
        'driver' => 'file',
        'ttl' => 3600,
    ],
    
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'channel' => 'default',
    ],
    
    'retry' => [
        'max_attempts' => 3,
        'delay' => 1000, // 毫秒
        'multiplier' => 2.0,
    ],
    
    'enable_global_config_function' => false,
];