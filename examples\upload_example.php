<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Fdmc\FileServiceClient\FileServiceClient;
use Fdmc\FileServiceClient\FileServiceException;

/**
 * 檔案上傳功能示例
 * 展示如何使用 FileServiceClient 的各種上傳方法
 */

echo "=== 檔案上傳功能示例 ===\n\n";

// 初始化客戶端
$client = new FileServiceClient([
    'base_uri' => 'https://api.fileservice.com',
    'timeout' => 120, // 上傳可能需要更長時間
]);

$client->setHeaders([
    'Authorization' => 'Bearer your-access-token'
]);

$companyId = 1;
$scope = 'uploads';
$userId = 123;

try {
    // ========================================
    // 範例 1: 基本檔案上傳（從檔案路徑）
    // ========================================
    echo "📤 範例 1: 基本檔案上傳\n";
    echo "----------------------------------------\n";

    // 創建測試檔案
    $testFilePath = sys_get_temp_dir() . '/test_upload.txt';
    file_put_contents($testFilePath, "這是一個測試上傳檔案\n建立時間: " . date('Y-m-d H:i:s'));

    // 步驟 1: 建立檔案物件
    $objectData = $client->buildObjectData(
        companyId: $companyId,
        scope: $scope,
        name: 'test_upload.txt',
        type: 'file',
        parent: null, // 上傳到根目錄
        mimeType: 'text/plain'
    );

    $object = $client->createObject($userId, $objectData);
    $objectId = $object['data']['id'];
    echo "✅ 檔案物件已創建: {$objectId}\n";

    // 步驟 2: 上傳檔案內容
    $uploadResult = $client->uploadFile($userId, $testFilePath, $objectId);

    echo "✅ 檔案內容上傳成功!\n";
    echo "   檔案 ID: " . $objectId . "\n";
    echo "   上傳結果: " . json_encode($uploadResult) . "\n\n";

    // 清理測試檔案
    unlink($testFilePath);

    // ========================================
    // 範例 2: 從內容上傳檔案
    // ========================================
    echo "📝 範例 2: 從內容上傳檔案\n";
    echo "----------------------------------------\n";

    $fileContent = json_encode([
        'message' => 'Hello from PHP FileServiceClient!',
        'timestamp' => time(),
        'data' => [
            'user_id' => $userId,
            'company_id' => $companyId,
            'features' => ['upload', 'download', 'version_control']
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

    // 步驟 1: 建立檔案物件
    $objectData = $client->buildObjectData(
        companyId: $companyId,
        scope: $scope,
        name: 'api_data.json',
        type: 'file',
        mimeType: 'application/json'
    );

    $object = $client->createObject($userId, $objectData);
    $objectId = $object['data']['id'];
    echo "✅ JSON 檔案物件已創建: {$objectId}\n";

    // 步驟 2: 上傳內容
    $uploadResult = $client->uploadFileFromContent($userId, $fileContent, 'api_data.json', $objectId);

    echo "✅ 檔案內容上傳成功!\n";
    echo "   檔案 ID: " . $objectId . "\n";
    echo "   內容大小: " . strlen($fileContent) . " bytes\n\n";

    // ========================================
    // 範例 3: 帶進度回調的上傳
    // ========================================
    echo "📊 範例 3: 帶進度回調的上傳\n";
    echo "----------------------------------------\n";

    // 創建一個較大的測試檔案
    $largeFilePath = sys_get_temp_dir() . '/large_test.txt';
    $largeContent = str_repeat("這是一行測試內容，用於模擬大檔案上傳。\n", 1000);
    file_put_contents($largeFilePath, $largeContent);

    // 進度回調函數
    $progressCallback = function ($downloadTotal, $downloadedBytes, $uploadTotal, $uploadedBytes) {
        if ($uploadTotal > 0) {
            $percentage = round(($uploadedBytes / $uploadTotal) * 100, 2);
            echo "   上傳進度: {$percentage}% ({$uploadedBytes}/{$uploadTotal} bytes)\r";
        }
    };

    // 步驟 1: 建立大檔案物件
    $objectData = $client->buildObjectData(
        companyId: $companyId,
        scope: $scope,
        name: 'large_test.txt',
        type: 'file',
        mimeType: 'text/plain'
    );

    $object = $client->createObject($userId, $objectData);
    $objectId = $object['data']['id'];
    echo "✅ 大檔案物件已創建: {$objectId}\n";

    // 步驟 2: 帶進度監控的上傳
    echo "開始上傳大檔案...\n";
    $uploadResult = $client->uploadFile($userId, $largeFilePath, $objectId, $progressCallback);

    echo "\n✅ 大檔案上傳成功!\n";
    echo "   檔案 ID: " . $objectId . "\n";
    echo "   原始大小: " . strlen($largeContent) . " bytes\n";
    echo "   上傳結果: " . json_encode($uploadResult) . "\n\n";

    // 清理測試檔案
    unlink($largeFilePath);

    // ========================================
    // 範例 4: 從串流上傳檔案
    // ========================================
    echo "🌊 範例 4: 從串流上傳檔案\n";
    echo "----------------------------------------\n";

    // 創建記憶體串流
    $stream = fopen('php://memory', 'r+');
    $csvData = "姓名,年齡,部門\n" .
        "張三,28,技術部\n" .
        "李四,32,行銷部\n" .
        "王五,25,設計部\n";

    fwrite($stream, $csvData);
    rewind($stream);

    // 步驟 1: 建立 CSV 檔案物件
    $objectData = $client->buildObjectData(
        companyId: $companyId,
        scope: $scope,
        name: 'employees.csv',
        type: 'file',
        mimeType: 'text/csv'
    );

    $object = $client->createObject($userId, $objectData);
    $objectId = $object['data']['id'];
    echo "✅ CSV 檔案物件已創建: {$objectId}\n";

    // 步驟 2: 從串流上傳
    $uploadResult = $client->uploadFileFromStream($userId, $stream, 'employees.csv', $objectId);

    echo "✅ 串流檔案上傳成功!\n";
    echo "   檔案 ID: " . $objectId . "\n";
    echo "   上傳結果: " . json_encode($uploadResult) . "\n\n";

    fclose($stream);

    echo "🎉 所有上傳範例完成!\n";

} catch (FileServiceException $e) {
    echo "❌ 檔案服務錯誤: " . $e->getMessage() . "\n";
    echo "   錯誤代碼: " . $e->getCode() . "\n";

    if ($e->getPrevious()) {
        echo "   原始錯誤: " . $e->getPrevious()->getMessage() . "\n";
    }
} catch (Exception $e) {
    echo "❌ 系統錯誤: " . $e->getMessage() . "\n";
}

/**
 * 上傳功能的最佳實踐：
 * 
 * 📋 兩步驟上傳流程：
 * 1. 先使用 createObject() 建立檔案物件
 * 2. 再使用上傳方法將內容上傳到該物件
 * 
 * 💡 實踐要點：
 * 
 * 1. 📁 檔案驗證：
 *    - 檢查檔案是否存在和可讀
 *    - 驗證檔案大小限制
 *    - 檢查檔案類型和 MIME 類型
 * 
 * 2. 🔄 進度監控：
 *    - 使用進度回調顯示上傳狀態
 *    - 對大檔案特別有用
 *    - 改善使用者體驗
 * 
 * 3. ⚡ 效能優化：
 *    - 對大檔案使用串流上傳
 *    - 適當設置超時時間
 *    - 選擇合適的 multipart 上傳方式
 * 
 * 4. 🛡️ 錯誤處理：
 *    - 捕獲和處理上傳錯誤
 *    - 提供有意義的錯誤訊息
 *    - 實施重試機制（如需要）
 * 
 * 5. 🧹 資源管理：
 *    - 及時關閉檔案資源
 *    - 清理臨時檔案
 *    - 管理記憶體使用
 * 
 * 6. 🔗 API 設計：
 *    - 先創建物件再上傳內容的設計
 *    - 支援中斷後重新上傳到已有物件
 *    - 更好的錯誤恢復機制
 */