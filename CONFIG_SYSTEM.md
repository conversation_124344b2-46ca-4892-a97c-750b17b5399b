# 配置系統說明

File Service Client 現在支援 Laravel 風格的配置系統，提供更靈活和可維護的配置管理方式。

## 配置檔案

配置檔案位於 `config/file-service.php`，包含所有預設配置選項：

```php
<?php

return [
    // API 基礎 URL
    'baseUrl' => env('FILE_SERVICE_BASE_URL', 'http://localhost:8080'),

    // HTTP 客戶端配置
    'http' => [
        'timeout' => env('FILE_SERVICE_TIMEOUT', 30),
        'connect_timeout' => env('FILE_SERVICE_CONNECT_TIMEOUT', 10),
        'read_timeout' => env('FILE_SERVICE_READ_TIMEOUT', 60),
        'verify' => env('FILE_SERVICE_SSL_VERIFY', false),
        'http_errors' => false,
    ],

    // 預設 HTTP 標頭
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'User-Agent' => 'FDMC-FileServiceClient/1.0',
    ],

    // 更多配置...
];
```

## 使用方式

### 1. 使用預設配置

```php
use FDMC\FileServiceClient\FileServiceClient;

// 使用配置檔案中的預設值
$client = new FileServiceClient();
```

### 2. 覆蓋特定配置

```php
use FDMC\FileServiceClient\FileServiceClient;

// 使用者選項會與配置檔案合併，使用者選項優先
$client = new FileServiceClient([
    'baseUrl' => 'https://api.example.com',
    'http' => [
        'timeout' => 60, // 只覆蓋 timeout，其他 http 配置保持預設
    ],
    'headers' => [
        'Authorization' => 'Bearer your-token', // 新增標頭，預設標頭仍保留
    ],
]);
```

### 3. 直接使用 ConfigManager

```php
use FDMC\FileServiceClient\ConfigManager;

// 獲取所有配置
$allConfig = ConfigManager::all();

// 獲取特定配置值（支援點號分隔的巢狀鍵）
$timeout = ConfigManager::get('http.timeout');
$baseUrl = ConfigManager::get('baseUrl', 'http://localhost:8080'); // 帶預設值

// 獲取專用配置
$uploadConfig = ConfigManager::getUploadConfig();
$downloadConfig = ConfigManager::getDownloadConfig();

// 合併使用者選項
$mergedConfig = ConfigManager::mergeWithUserOptions([
    'baseUrl' => 'https://custom.api.com',
]);
```

## 配置優先級

配置系統遵循以下優先級順序（高到低）：

1. **使用者選項** - 傳遞給建構子的 `$options` 陣列
2. **環境變數** - 透過 `env()` 函數讀取
3. **配置檔案** - `config/file-service.php` 中的預設值

### 範例

```php
// 配置檔案中設置
'baseUrl' => env('FILE_SERVICE_BASE_URL', 'http://localhost:8080'),

// 環境變數設置
FILE_SERVICE_BASE_URL=https://env.api.com

// 使用者選項
$client = new FileServiceClient([
    'baseUrl' => 'https://user.api.com', // 這個會覆蓋環境變數
]);

// 最終結果：https://user.api.com
```

## 環境變數

支援的環境變數：

- `FILE_SERVICE_BASE_URL` - API 基礎 URL
- `FILE_SERVICE_TIMEOUT` - 請求超時時間
- `FILE_SERVICE_CONNECT_TIMEOUT` - 連接超時時間
- `FILE_SERVICE_READ_TIMEOUT` - 讀取超時時間
- `FILE_SERVICE_SSL_VERIFY` - SSL 驗證開關
- `FILE_SERVICE_UPLOAD_TIMEOUT` - 上傳超時時間
- `FILE_SERVICE_MAX_FILE_SIZE` - 最大檔案大小
- `FILE_SERVICE_DOWNLOAD_TIMEOUT` - 下載超時時間
- `FILE_SERVICE_CACHE_ENABLED` - 快取開關
- `FILE_SERVICE_LOG_ENABLED` - 日誌開關

## 配置合併邏輯

配置系統使用遞迴合併，確保巢狀陣列正確合併：

```php
// 配置檔案
[
    'http' => [
        'timeout' => 30,
        'connect_timeout' => 10,
        'verify' => false,
    ],
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
    ],
]

// 使用者選項
[
    'http' => [
        'timeout' => 60, // 覆蓋
        // connect_timeout 和 verify 保持預設值
    ],
    'headers' => [
        'Authorization' => 'Bearer token', // 新增
        // Content-Type 和 Accept 保持預設值
    ],
]

// 合併結果
[
    'http' => [
        'timeout' => 60,           // 來自使用者選項
        'connect_timeout' => 10,   // 來自配置檔案
        'verify' => false,         // 來自配置檔案
    ],
    'headers' => [
        'Content-Type' => 'application/json',  // 來自配置檔案
        'Accept' => 'application/json',        // 來自配置檔案
        'Authorization' => 'Bearer token',     // 來自使用者選項
    ],
]
```

## 快取機制

ConfigManager 內建配置快取機制，避免重複讀取配置檔案：

```php
// 清除快取（通常不需要手動調用）
ConfigManager::clearCache();
```

## 測試

配置系統包含完整的單元測試，確保各種情況下的正確行為：

```bash
./vendor/bin/phpunit tests/ConfigManagerTest.php
```

## 向後相容性

新的配置系統完全向後相容，現有程式碼無需修改即可使用：

```php
// 舊的使用方式仍然有效
$client = new FileServiceClient([
    'baseUrl' => 'https://api.example.com',
    'timeout' => 30,
]);
```

## 最佳實踐

1. **使用配置檔案設置預設值**，避免在程式碼中硬編碼
2. **使用環境變數**處理不同環境的配置差異
3. **使用使用者選項**進行特定情況的配置覆蓋
4. **利用巢狀配置**組織相關的配置選項
5. **定期檢查配置檔案**，確保配置項目是最新的