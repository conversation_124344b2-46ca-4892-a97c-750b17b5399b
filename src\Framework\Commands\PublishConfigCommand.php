<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Framework\Commands;

use FDMC\FileServiceClient\Processing\ConfigPublisher;
use FDMC\FileServiceClient\Logging\ConfigLogger;
use FDMC\FileServiceClient\Exceptions\ConfigurationException;

/**
 * 配置發布命令
 * 
 * 提供命令列介面來發布配置檔案到原生 PHP 專案
 */
class PublishConfigCommand
{
    private ConfigPublisher $publisher;
    private ConfigLogger $logger;

    public function __construct()
    {
        $this->logger = ConfigLogger::getInstance();
        $this->publisher = new ConfigPublisher($this->logger);
    }

    /**
     * 執行發布命令
     * 
     * @param array $args 命令列參數
     * @return int 退出碼（0 = 成功，1 = 失敗）
     */
    public function execute(array $args = []): int
    {
        try {
            $this->displayHeader();

            // 解析命令列參數
            $options = $this->parseArguments($args);

            // 檢查發布狀態
            $status = $this->publisher->getPublishStatus($options['target']);
            $this->displayStatus($status);

            // 如果檔案已存在且未強制覆蓋，詢問使用者
            if ($status['published'] && !$options['force']) {
                if (!$this->confirmPublish($status['target_path'])) {
                    $this->output("取消發布操作。");
                    return 0;
                }
                $options['force'] = true;
            }

            // 執行發布
            $success = $this->publisher->publishToNative($options['target'], $options['force']);

            if ($success) {
                $this->output("✓ 配置檔案發布成功！");
                $this->displayNextSteps($options['target'] ?? $this->getDefaultTargetPath());
                return 0;
            } else {
                $this->output("✗ 配置檔案發布失敗。");
                return 1;
            }

        } catch (ConfigurationException $e) {
            $this->output("✗ 發布失敗：" . $e->getMessage());
            $this->displayRepairSuggestions($e->getRepairSuggestions());
            return 1;
        } catch (\Exception $e) {
            $this->output("✗ 未預期的錯誤：" . $e->getMessage());
            return 1;
        }
    }

    /**
     * 解析命令列參數
     * 
     * @param array $args 命令列參數
     * @return array 解析後的選項
     */
    private function parseArguments(array $args): array
    {
        $options = [
            'target' => null,
            'force' => false,
            'help' => false,
        ];

        for ($i = 0; $i < count($args); $i++) {
            $arg = $args[$i];

            switch ($arg) {
                case '--target':
                case '-t':
                    if (isset($args[$i + 1])) {
                        $options['target'] = $args[$i + 1];
                        $i++; // 跳過下一個參數
                    }
                    break;

                case '--force':
                case '-f':
                    $options['force'] = true;
                    break;

                case '--help':
                case '-h':
                    $options['help'] = true;
                    break;
            }
        }

        if ($options['help']) {
            $this->displayHelp();
            exit(0);
        }

        return $options;
    }

    /**
     * 顯示標題
     */
    private function displayHeader(): void
    {
        $this->output("File Service Client - 配置發布工具");
        $this->output("=====================================");
        $this->output("");
    }

    /**
     * 顯示發布狀態
     * 
     * @param array $status 狀態資訊
     */
    private function displayStatus(array $status): void
    {
        $this->output("環境類型：" . $status['environment']);
        $this->output("目標路徑：" . $status['target_path']);

        if ($status['published']) {
            $this->output("狀態：✓ 配置檔案已存在");
            $this->output("檔案大小：" . $this->formatFileSize($status['file_size']));
            $this->output("修改時間：" . date('Y-m-d H:i:s', $status['modified_time']));
            $this->output("可讀：" . ($status['readable'] ? '是' : '否'));
            $this->output("可寫：" . ($status['writable'] ? '是' : '否'));
        } else {
            $this->output("狀態：- 配置檔案不存在");
        }

        $this->output("");
    }

    /**
     * 確認是否發布
     * 
     * @param string $targetPath 目標路徑
     * @return bool 是否確認發布
     */
    private function confirmPublish(string $targetPath): bool
    {
        $this->output("配置檔案已存在：{$targetPath}");
        $this->output("是否要覆蓋現有檔案？ [y/N]: ", false);

        $handle = fopen('php://stdin', 'r');
        if (!$handle) {
            return false;
        }

        $input = trim(fgets($handle));
        fclose($handle);

        return in_array(strtolower($input), ['y', 'yes', '是'], true);
    }

    /**
     * 顯示下一步操作
     * 
     * @param string $configPath 配置檔案路徑
     */
    private function displayNextSteps(string $configPath): void
    {
        $this->output("");
        $this->output("下一步操作：");
        $this->output("1. 編輯配置檔案：{$configPath}");
        $this->output("2. 根據您的需求調整配置項目");
        $this->output("3. 設定環境變數（可選）：");
        $this->output("   export FILE_SERVICE_BASE_URL=https://your-api.example.com");
        $this->output("4. 在您的 PHP 程式中使用：");
        $this->output("   require_once 'vendor/autoload.php';");
        $this->output("   \$config = file_service_config('baseUrl');");
    }

    /**
     * 顯示修復建議
     * 
     * @param array $suggestions 修復建議
     */
    private function displayRepairSuggestions(array $suggestions): void
    {
        if (empty($suggestions)) {
            return;
        }

        $this->output("");
        $this->output("修復建議：");
        foreach ($suggestions as $suggestion) {
            $this->output("• " . $suggestion);
        }
    }

    /**
     * 顯示幫助資訊
     */
    private function displayHelp(): void
    {
        $this->output("File Service Client - 配置發布工具");
        $this->output("");
        $this->output("用法：");
        $this->output("  php publish-config.php [選項]");
        $this->output("");
        $this->output("選項：");
        $this->output("  -t, --target PATH    指定目標配置檔案路徑");
        $this->output("  -f, --force          強制覆蓋現有檔案");
        $this->output("  -h, --help           顯示此幫助資訊");
        $this->output("");
        $this->output("範例：");
        $this->output("  php publish-config.php");
        $this->output("  php publish-config.php --target /path/to/config/file-service.php");
        $this->output("  php publish-config.php --force");
    }

    /**
     * 輸出訊息
     * 
     * @param string $message 訊息內容
     * @param bool $newline 是否換行
     */
    private function output(string $message, bool $newline = true): void
    {
        echo $message;
        if ($newline) {
            echo "\n";
        }
    }

    /**
     * 格式化檔案大小
     * 
     * @param int $bytes 位元組數
     * @return string 格式化後的大小
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen((string) $bytes) - 1) / 3);

        return sprintf("%.1f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * 獲取預設目標路徑
     * 
     * @return string 預設目標路徑
     */
    private function getDefaultTargetPath(): string
    {
        try {
            $projectRoot = $this->findProjectRoot();
            return $projectRoot . DIRECTORY_SEPARATOR . 'config' . DIRECTORY_SEPARATOR . 'file-service.php';
        } catch (\Exception $e) {
            return getcwd() . DIRECTORY_SEPARATOR . 'config' . DIRECTORY_SEPARATOR . 'file-service.php';
        }
    }

    /**
     * 尋找專案根目錄
     * 
     * @return string 專案根目錄路徑
     * @throws \Exception 當找不到專案根目錄時
     */
    private function findProjectRoot(): string
    {
        $currentDir = getcwd();
        if (!$currentDir) {
            throw new \Exception('無法獲取當前工作目錄');
        }

        $searchPaths = [
            $currentDir,
            dirname($currentDir),
            dirname(dirname($currentDir)),
        ];

        foreach ($searchPaths as $path) {
            $composerPath = $path . DIRECTORY_SEPARATOR . 'composer.json';
            if (file_exists($composerPath)) {
                return $path;
            }
        }

        return $currentDir;
    }
}