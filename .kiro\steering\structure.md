# 專案結構

## 目錄組織

```
file-service-client/
├── .kiro/                          # Kiro AI 助手配置
│   └── steering/                   # 引導規則文件
├── src/                            # 主要原始碼
│   ├── FileServiceClient.php       # 主要 API 入口點
│   ├── FileServiceException.php    # 自訂例外類別
│   ├── StorageManager.php          # 儲存管理（獨立模組）
│   └── Http/                       # HTTP 傳輸層
│       ├── HttpClientInterface.php # HTTP 客戶端介面
│       └── GuzzleHttpClient.php    # Guzzle HTTP 實現
├── tests/                          # 單元測試
│   ├── FileServiceClientTest.php   # 主要客戶端測試
│   └── StorageManagerTest.php      # 儲存管理測試
├── examples/                       # 使用範例
│   ├── http_client_usage.php       # HTTP 客戶端使用示例
│   ├── upload_example.php          # 檔案上傳範例
│   └── architecture_comparison.php # 架構設計比較
├── vendor/                         # Composer 依賴（自動生成）
├── composer.json                   # Composer 配置
├── phpunit.xml                     # PHPUnit 測試配置
└── README.md                       # 專案說明文件
```

## 核心類別結構

### 主要入口點
- **FileServiceClient**: 統一的 API 入口點，提供所有檔案操作功能
- **StorageManager**: 獨立的儲存管理模組

### HTTP 層
- **HttpClientInterface**: 定義 HTTP 通訊的抽象介面
- **GuzzleHttpClient**: 基於 Guzzle 的 HTTP 客戶端實現

### 例外處理
- **FileServiceException**: 統一的例外處理類別

## 架構分層

### 1. 業務邏輯層 (Business Logic Layer)
- `FileServiceClient.php` - 檔案服務業務邏輯
- `StorageManager.php` - 儲存管理業務邏輯

### 2. HTTP 傳輸層 (HTTP Transport Layer)
- `HttpClientInterface.php` - HTTP 抽象介面
- `GuzzleHttpClient.php` - HTTP 具體實現

### 3. 例外處理層 (Exception Layer)
- `FileServiceException.php` - 統一例外處理

## 設計模式

### 依賴注入 (Dependency Injection)
- 支援自訂 HTTP 客戶端注入
- 便於單元測試和模擬

### 介面隔離 (Interface Segregation)
- HTTP 客戶端使用介面抽象
- 業務邏輯與 HTTP 實現解耦

### 單一職責 (Single Responsibility)
- 每個類別專注於特定功能
- HTTP 層與業務邏輯分離

## 命名規範

### 類別命名
- 使用 PascalCase：`FileServiceClient`、`HttpClientInterface`
- 例外類別以 `Exception` 結尾：`FileServiceException`

### 方法命名
- 使用 camelCase：`listRootObjects()`、`uploadFile()`
- 動詞開頭，描述具體動作

### 常數命名
- 使用 UPPER_SNAKE_CASE
- 在類別內部定義相關常數

## 檔案組織原則

### 按功能分組
- 相關功能的類別放在同一目錄
- HTTP 相關類別在 `Http/` 目錄下

### 測試對應
- 每個主要類別都有對應的測試檔案
- 測試檔案命名：`{ClassName}Test.php`

### 範例分類
- 按使用場景組織範例檔案
- 包含架構說明和最佳實踐