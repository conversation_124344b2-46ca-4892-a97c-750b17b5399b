<?php

/**
 * Laravel 服務提供者使用範例
 * 
 * 此範例展示如何在 Laravel 專案中使用 File Service 配置系統
 */

// 載入 Composer 自動載入器
require_once __DIR__ . '/../vendor/autoload.php';

// 注意：這個範例假設在 Laravel 環境中執行

echo "=== Laravel 服務提供者使用範例 ===\n\n";

// 1. 服務提供者註冊
echo "1. 服務提供者註冊\n";
echo "在 config/app.php 中註冊服務提供者：\n";
echo "```php\n";
echo "'providers' => [\n";
echo "    // 其他服務提供者...\n";
echo "    FDMC\\FileServiceClient\\FileServiceConfigServiceProvider::class,\n";
echo "],\n";
echo "```\n\n";

// 2. 發布配置檔案
echo "2. 發布配置檔案\n";
echo "執行以下 Artisan 指令來發布配置檔案：\n";
echo "```bash\n";
echo "php artisan vendor:publish --tag=file-service-config\n";
echo "```\n";
echo "這會將配置檔案複製到 config/file-service.php\n\n";

// 3. 環境變數配置
echo "3. 環境變數配置\n";
echo "在 .env 檔案中設定環境變數：\n";
echo "```env\n";
echo "FILE_SERVICE_BASE_URL=https://api.example.com\n";
echo "FILE_SERVICE_TIMEOUT=30\n";
echo "FILE_SERVICE_SSL_VERIFY=true\n";
echo "FILE_SERVICE_ENABLE_GLOBAL_CONFIG_FUNCTION=false\n";
echo "```\n\n";

// 4. 在 Laravel 中使用配置
echo "4. 在 Laravel 中使用配置\n";

// 模擬 Laravel 的 config 函數（實際使用時不需要這個）
if (!function_exists('config')) {
    function config($key = null, $default = null) {
        // 模擬 Laravel 配置
        $mockConfig = [
            'file-service' => [
                'baseUrl' => 'https://api.example.com',
                'http' => [
                    'timeout' => 30,
                    'connect_timeout' => 10,
                ],
                'enable_global_config_function' => false,
            ]
        ];
        
        if ($key === null) {
            return $mockConfig;
        }
        
        $keys = explode('.', $key);
        $value = $mockConfig;
        
        foreach ($keys as $nestedKey) {
            if (!isset($value[$nestedKey])) {
                return $default;
            }
            $value = $value[$nestedKey];
        }
        
        return $value;
    }
}

// 使用 Laravel 的 config 函數存取 File Service 配置
echo "使用 Laravel 的 config() 函數：\n";
$baseUrl = config('file-service.baseUrl');
$timeout = config('file-service.http.timeout', 30);
$sslVerify = config('file-service.http.verify', false);

echo "Base URL: {$baseUrl}\n";
echo "Timeout: {$timeout} 秒\n";
echo "SSL Verify: " . ($sslVerify ? 'true' : 'false') . "\n\n";

// 5. 使用依賴注入
echo "5. 使用依賴注入\n";
echo "在控制器或服務中注入 GlobalConfigManager：\n";
echo "```php\n";
echo "use FDMC\\FileServiceClient\\GlobalConfigManager;\n";
echo "\n";
echo "class FileController extends Controller\n";
echo "{\n";
echo "    public function __construct(\n";
echo "        private GlobalConfigManager \$configManager\n";
echo "    ) {}\n";
echo "\n";
echo "    public function upload(Request \$request)\n";
echo "    {\n";
echo "        \$maxFileSize = \$this->configManager->config('upload.max_file_size');\n";
echo "        // 檔案上傳邏輯...\n";
echo "    }\n";
echo "}\n";
echo "```\n\n";

// 6. 全域函數使用（可選）
echo "6. 全域函數使用（可選）\n";
echo "如果啟用了全域函數，可以使用 file_service_config()：\n";

// 載入全域函數
require_once __DIR__ . '/../src/helpers.php';

// 模擬 env 函數（在實際 Laravel 環境中不需要）
if (!function_exists('env')) {
    function env($key, $default = null) {
        return $_ENV[$key] ?? getenv($key) ?: $default;
    }
}

echo "使用 file_service_config() 函數：\n";
try {
    // 注意：在實際 Laravel 環境中，這會自動使用 Laravel 的配置系統
    $allConfig = file_service_config();
    echo "所有配置項目數量: " . count($allConfig) . "\n";
    
    $baseUrl = file_service_config('baseUrl', 'http://localhost:8080');
    echo "Base URL: {$baseUrl}\n";
    
    $timeout = file_service_config('http.timeout', 30);
    echo "HTTP Timeout: {$timeout} 秒\n";
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}

echo "\n";

// 7. 配置驗證
echo "7. 配置驗證\n";
echo "服務提供者會自動驗證配置格式：\n";
echo "- 檢查必要的配置項目\n";
echo "- 驗證配置值的型別\n";
echo "- 提供預設值\n";
echo "- 記錄配置錯誤\n\n";

// 8. 日誌記錄
echo "8. 日誌記錄\n";
echo "服務提供者會記錄以下資訊：\n";
echo "- 全域函數註冊狀態\n";
echo "- 配置衝突警告\n";
echo "- 配置載入錯誤\n";
echo "這些日誌會寫入 Laravel 的日誌系統中\n\n";

// 9. 測試環境配置
echo "9. 測試環境配置\n";
echo "在測試中可以動態修改配置：\n";
echo "```php\n";
echo "// 在測試中\n";
echo "config(['file-service.baseUrl' => 'http://test-api.example.com']);\n";
echo "\n";
echo "// 或使用 file_service_config\n";
echo "file_service_config('baseUrl', 'http://test-api.example.com');\n";
echo "```\n\n";

// 10. 效能考量
echo "10. 效能考量\n";
echo "- 配置在 Laravel 啟動時載入並快取\n";
echo "- 運行時修改不會影響檔案系統\n";
echo "- 使用 Laravel 的配置快取機制\n";
echo "- 支援配置快取指令：php artisan config:cache\n\n";

echo "=== 範例結束 ===\n";