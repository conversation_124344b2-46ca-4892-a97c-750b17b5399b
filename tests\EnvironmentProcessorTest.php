<?php

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\Processing\EnvironmentProcessor;
use FDMC\FileServiceClient\Exceptions\TypeConversionException;
use PHPUnit\Framework\TestCase;

class EnvironmentProcessorTest extends TestCase
{
    private EnvironmentProcessor $processor;

    protected function setUp(): void
    {
        $this->processor = new EnvironmentProcessor();

        // 清理環境變數
        $this->clearEnvironmentVariables();
    }

    protected function tearDown(): void
    {
        // 清理環境變數
        $this->clearEnvironmentVariables();
    }

    /**
     * 測試環境變數覆蓋配置 - 需求 5.1
     */
    public function testProcessEnvVarsOverridesConfig(): void
    {
        // 設定環境變數
        $_ENV['FILE_SERVICE_BASE_URL'] = 'https://api.example.com';
        $_ENV['FILE_SERVICE_TIMEOUT'] = '60';
        $_ENV['FILE_SERVICE_SSL_VERIFY'] = 'false';

        $config = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'verify' => true,
            ],
        ];

        $result = $this->processor->processEnvVars($config);

        $this->assertEquals('https://api.example.com', $result['baseUrl']);
        $this->assertEquals(60, $result['http']['timeout']);
        $this->assertFalse($result['http']['verify']);
    }

    /**
     * 測試環境變數不存在時使用預設值 - 需求 5.2
     */
    public function testProcessEnvVarsUsesDefaultWhenEnvNotSet(): void
    {
        $config = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'verify' => true,
            ],
        ];

        $result = $this->processor->processEnvVars($config);

        // 環境變數不存在，應該保持原始配置
        $this->assertEquals('http://localhost:8080', $result['baseUrl']);
        $this->assertEquals(30, $result['http']['timeout']);
        $this->assertTrue($result['http']['verify']);
    }

    /**
     * 測試布林值字串轉換 - 需求 5.3
     */
    public function testParseBooleanCorrectly(): void
    {
        // 測試 true 值
        $this->assertTrue($this->processor->parseBoolean('true'));
        $this->assertTrue($this->processor->parseBoolean('TRUE'));
        $this->assertTrue($this->processor->parseBoolean('1'));
        $this->assertTrue($this->processor->parseBoolean('yes'));
        $this->assertTrue($this->processor->parseBoolean('YES'));
        $this->assertTrue($this->processor->parseBoolean('on'));
        $this->assertTrue($this->processor->parseBoolean('ON'));

        // 測試 false 值
        $this->assertFalse($this->processor->parseBoolean('false'));
        $this->assertFalse($this->processor->parseBoolean('FALSE'));
        $this->assertFalse($this->processor->parseBoolean('0'));
        $this->assertFalse($this->processor->parseBoolean('no'));
        $this->assertFalse($this->processor->parseBoolean('NO'));
        $this->assertFalse($this->processor->parseBoolean('off'));
        $this->assertFalse($this->processor->parseBoolean('OFF'));
        $this->assertFalse($this->processor->parseBoolean(''));
        $this->assertFalse($this->processor->parseBoolean('   '));
    }

    /**
     * 測試無效布林值拋出例外
     */
    public function testParseBooleanThrowsExceptionForInvalidValue(): void
    {
        $this->expectException(TypeConversionException::class);
        $this->expectExceptionMessage("無法將 'invalid' 轉換為布林值");

        $this->processor->parseBoolean('invalid');
    }

    /**
     * 測試數值字串轉換 - 需求 5.4
     */
    public function testParseIntegerCorrectly(): void
    {
        $this->assertEquals(123, $this->processor->parseInteger('123'));
        $this->assertEquals(0, $this->processor->parseInteger('0'));
        $this->assertEquals(999, $this->processor->parseInteger('  999  '));
    }

    /**
     * 測試無效整數拋出例外
     */
    public function testParseIntegerThrowsExceptionForInvalidValue(): void
    {
        $this->expectException(TypeConversionException::class);
        $this->expectExceptionMessage("無法將 '123.45' 轉換為整數");

        $this->processor->parseInteger('123.45');
    }

    /**
     * 測試負數整數拋出例外
     */
    public function testParseIntegerThrowsExceptionForNegativeValue(): void
    {
        $this->expectException(TypeConversionException::class);
        $this->expectExceptionMessage("無法將 '-10' 轉換為整數");

        $this->processor->parseInteger('-10');
    }

    /**
     * 測試浮點數轉換
     */
    public function testParseFloatCorrectly(): void
    {
        $this->assertEquals(123.45, $this->processor->parseFloat('123.45'));
        $this->assertEquals(0.0, $this->processor->parseFloat('0'));
        $this->assertEquals(999.99, $this->processor->parseFloat('  999.99  '));
    }

    /**
     * 測試無效浮點數拋出例外
     */
    public function testParseFloatThrowsExceptionForInvalidValue(): void
    {
        $this->expectException(TypeConversionException::class);
        $this->expectExceptionMessage("無法將 'invalid' 轉換為浮點數");

        $this->processor->parseFloat('invalid');
    }

    /**
     * 測試 JSON 陣列解析
     */
    public function testParseArrayCorrectly(): void
    {
        $this->assertEquals(['a', 'b', 'c'], $this->processor->parseArray('["a","b","c"]'));
        $this->assertEquals(['key' => 'value'], $this->processor->parseArray('{"key":"value"}'));
        $this->assertEquals([], $this->processor->parseArray('[]'));
        $this->assertEquals([], $this->processor->parseArray(''));
        $this->assertEquals([], $this->processor->parseArray('   '));
    }

    /**
     * 測試無效 JSON 拋出例外
     */
    public function testParseArrayThrowsExceptionForInvalidJson(): void
    {
        $this->expectException(TypeConversionException::class);
        $this->expectExceptionMessageMatches('/無法將.*轉換為陣列/');

        $this->processor->parseArray('invalid json');
    }

    /**
     * 測試非陣列 JSON 拋出例外
     */
    public function testParseArrayThrowsExceptionForNonArrayJson(): void
    {
        $this->expectException(TypeConversionException::class);
        $this->expectExceptionMessageMatches('/無法將.*轉換為陣列/');

        $this->processor->parseArray('"string"');
    }

    /**
     * 測試字串解析
     */
    public function testParseStringCorrectly(): void
    {
        $this->assertEquals('hello', $this->processor->parseString('hello'));
        $this->assertEquals('world', $this->processor->parseString('  world  '));
        $this->assertEquals('', $this->processor->parseString(''));
    }

    /**
     * 測試獲取預期型別
     */
    public function testGetExpectedType(): void
    {
        $this->assertEquals('string', $this->processor->getExpectedType('baseUrl'));
        $this->assertEquals('integer', $this->processor->getExpectedType('http.timeout'));
        $this->assertEquals('boolean', $this->processor->getExpectedType('http.verify'));
        $this->assertEquals('string', $this->processor->getExpectedType('unknown.key'));
    }

    /**
     * 測試環境變數型別轉換
     */
    public function testProcessEnvironmentVariable(): void
    {
        // 測試字串
        $this->assertEquals('test', $this->processor->processEnvironmentVariable('baseUrl', 'test'));

        // 測試整數
        $this->assertEquals(30, $this->processor->processEnvironmentVariable('http.timeout', '30'));

        // 測試布林值
        $this->assertTrue($this->processor->processEnvironmentVariable('http.verify', 'true'));
        $this->assertFalse($this->processor->processEnvironmentVariable('http.verify', 'false'));
    }

    /**
     * 測試型別轉換失敗時的錯誤處理
     */
    public function testProcessEnvironmentVariableHandlesTypeConversionError(): void
    {
        $this->expectException(TypeConversionException::class);

        $this->processor->processEnvironmentVariable('http.timeout', 'invalid');
    }

    /**
     * 測試完整的環境變數處理流程
     */
    public function testCompleteEnvironmentProcessing(): void
    {
        // 設定多種型別的環境變數
        $_ENV['FILE_SERVICE_BASE_URL'] = 'https://api.example.com';
        $_ENV['FILE_SERVICE_TIMEOUT'] = '60';
        $_ENV['FILE_SERVICE_SSL_VERIFY'] = 'false';
        $_ENV['FILE_SERVICE_CACHE_ENABLED'] = 'true';
        $_ENV['FILE_SERVICE_MAX_FILE_SIZE'] = '104857600';

        $config = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'verify' => true,
            ],
            'cache' => [
                'enabled' => false,
            ],
            'upload' => [
                'max_file_size' => 52428800,
            ],
        ];

        $result = $this->processor->processEnvVars($config);

        // 驗證所有環境變數都正確覆蓋了配置
        $this->assertEquals('https://api.example.com', $result['baseUrl']);
        $this->assertEquals(60, $result['http']['timeout']);
        $this->assertFalse($result['http']['verify']);
        $this->assertTrue($result['cache']['enabled']);
        $this->assertEquals(104857600, $result['upload']['max_file_size']);
    }

    /**
     * 測試環境變數型別轉換失敗時的錯誤處理
     */
    public function testProcessEnvVarsHandlesTypeConversionErrors(): void
    {
        // 設定無效的環境變數
        $_ENV['FILE_SERVICE_TIMEOUT'] = 'invalid_number';

        $config = [
            'http' => [
                'timeout' => 30,
            ],
        ];

        $result = $this->processor->processEnvVars($config);

        // 驗證保持了原始配置值（當型別轉換失敗時，忽略該環境變數）
        $this->assertEquals(30, $result['http']['timeout']);
    }

    /**
     * 清理環境變數
     */
    private function clearEnvironmentVariables(): void
    {
        $envVars = [
            'FILE_SERVICE_BASE_URL',
            'FILE_SERVICE_TIMEOUT',
            'FILE_SERVICE_CONNECT_TIMEOUT',
            'FILE_SERVICE_READ_TIMEOUT',
            'FILE_SERVICE_SSL_VERIFY',
            'FILE_SERVICE_UPLOAD_TIMEOUT',
            'FILE_SERVICE_MAX_FILE_SIZE',
            'FILE_SERVICE_DOWNLOAD_TIMEOUT',
            'FILE_SERVICE_CACHE_ENABLED',
            'FILE_SERVICE_CACHE_DRIVER',
            'FILE_SERVICE_CACHE_TTL',
            'FILE_SERVICE_LOG_ENABLED',
            'FILE_SERVICE_LOG_LEVEL',
            'FILE_SERVICE_LOG_CHANNEL',
            'FILE_SERVICE_RETRY_MAX_ATTEMPTS',
            'FILE_SERVICE_RETRY_DELAY',
            'FILE_SERVICE_RETRY_MULTIPLIER',
        ];

        foreach ($envVars as $var) {
            unset($_ENV[$var], $_SERVER[$var]);
            putenv($var);
        }
    }
}