<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Exceptions;

/**
 * 型別轉換例外類別
 * 
 * 專門處理環境變數和配置值的型別轉換錯誤。
 */
class TypeConversionException extends ConfigurationException
{
    // 型別轉換相關錯誤碼
    public const TYPE_BOOLEAN_CONVERSION_FAILED = 2001;
    public const TYPE_INTEGER_CONVERSION_FAILED = 2002;
    public const TYPE_FLOAT_CONVERSION_FAILED = 2003;
    public const TYPE_ARRAY_CONVERSION_FAILED = 2004;
    public const TYPE_STRING_CONVERSION_FAILED = 2005;
    public const TYPE_UNSUPPORTED_TYPE = 2006;

    /**
     * 原始值
     */
    private mixed $originalValue;

    /**
     * 目標型別
     */
    private string $targetType;

    /**
     * 配置鍵
     */
    private string $configKey;

    /**
     * 建構子
     *
     * @param string $message 錯誤訊息
     * @param int $code 錯誤碼
     * @param string $configKey 配置鍵
     * @param mixed $originalValue 原始值
     * @param string $targetType 目標型別
     * @param \Throwable|null $previous 前一個例外
     */
    public function __construct(
        string $message,
        int $code,
        string $configKey,
        mixed $originalValue,
        string $targetType,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->configKey = $configKey;
        $this->originalValue = $originalValue;
        $this->targetType = $targetType;
    }

    /**
     * 獲取原始值
     *
     * @return mixed 原始值
     */
    public function getOriginalValue(): mixed
    {
        return $this->originalValue;
    }

    /**
     * 獲取目標型別
     *
     * @return string 目標型別
     */
    public function getTargetType(): string
    {
        return $this->targetType;
    }

    /**
     * 獲取配置鍵
     *
     * @return string 配置鍵
     */
    public function getConfigKey(): string
    {
        return $this->configKey;
    }

    /**
     * 布林值轉換失敗
     *
     * @param string $key 配置鍵
     * @param mixed $value 原始值
     * @return self
     */
    public static function booleanConversionFailed(string $key, mixed $value): self
    {
        $valueStr = is_string($value) ? "'{$value}'" : var_export($value, true);
        return new self(
            "無法將 {$valueStr} 轉換為布林值",
            self::TYPE_BOOLEAN_CONVERSION_FAILED,
            $key,
            $value,
            'boolean'
        );
    }

    /**
     * 整數轉換失敗
     *
     * @param string $key 配置鍵
     * @param mixed $value 原始值
     * @return self
     */
    public static function integerConversionFailed(string $key, mixed $value): self
    {
        $valueStr = is_string($value) ? "'{$value}'" : var_export($value, true);
        return new self(
            "無法將 {$valueStr} 轉換為整數",
            self::TYPE_INTEGER_CONVERSION_FAILED,
            $key,
            $value,
            'integer'
        );
    }

    /**
     * 浮點數轉換失敗
     *
     * @param string $key 配置鍵
     * @param mixed $value 原始值
     * @return self
     */
    public static function floatConversionFailed(string $key, mixed $value): self
    {
        $valueStr = is_string($value) ? "'{$value}'" : var_export($value, true);
        return new self(
            "無法將 {$valueStr} 轉換為浮點數",
            self::TYPE_FLOAT_CONVERSION_FAILED,
            $key,
            $value,
            'float'
        );
    }

    /**
     * 陣列轉換失敗
     *
     * @param string $key 配置鍵
     * @param mixed $value 原始值
     * @param string $reason 失敗原因
     * @return self
     */
    public static function arrayConversionFailed(string $key, mixed $value, string $reason): self
    {
        $valueStr = is_string($value) ? "'{$value}'" : var_export($value, true);
        return new self(
            "無法將 {$valueStr} 轉換為陣列：{$reason}",
            self::TYPE_ARRAY_CONVERSION_FAILED,
            $key,
            $value,
            'array'
        );
    }

    /**
     * 字串轉換失敗
     *
     * @param string $key 配置鍵
     * @param mixed $value 原始值
     * @return self
     */
    public static function stringConversionFailed(string $key, mixed $value): self
    {
        $valueStr = var_export($value, true);
        return new self(
            "無法將 {$valueStr} 轉換為字串",
            self::TYPE_STRING_CONVERSION_FAILED,
            $key,
            $value,
            'string'
        );
    }

    /**
     * 不支援的型別
     *
     * @param string $key 配置鍵
     * @param mixed $value 原始值
     * @param string $type 不支援的型別
     * @return self
     */
    public static function unsupportedType(string $key, mixed $value, string $type): self
    {
        $valueStr = is_string($value) ? "'{$value}'" : var_export($value, true);
        return new self(
            "不支援的型別轉換：無法將 {$valueStr} 轉換為 {$type}",
            self::TYPE_UNSUPPORTED_TYPE,
            $key,
            $value,
            $type
        );
    }

    /**
     * 獲取錯誤碼對應的描述
     *
     * @return string 錯誤描述
     */
    public function getErrorDescription(): string
    {
        return match ($this->getCode()) {
            self::TYPE_BOOLEAN_CONVERSION_FAILED => '布林值轉換失敗',
            self::TYPE_INTEGER_CONVERSION_FAILED => '整數轉換失敗',
            self::TYPE_FLOAT_CONVERSION_FAILED => '浮點數轉換失敗',
            self::TYPE_ARRAY_CONVERSION_FAILED => '陣列轉換失敗',
            self::TYPE_STRING_CONVERSION_FAILED => '字串轉換失敗',
            self::TYPE_UNSUPPORTED_TYPE => '不支援的型別',
            default => parent::getErrorDescription()
        };
    }

    /**
     * 獲取修復建議
     *
     * @return array 修復建議列表
     */
    public function getRepairSuggestions(): array
    {
        return match ($this->getCode()) {
            self::TYPE_BOOLEAN_CONVERSION_FAILED => [
                '使用有效的布林值：true, false, 1, 0, yes, no, on, off',
                '檢查環境變數值是否正確設定',
                '確認配置檔案中的布林值格式'
            ],
            self::TYPE_INTEGER_CONVERSION_FAILED => [
                '確保值是有效的整數',
                '移除小數點和非數字字符',
                '檢查數值範圍是否在 PHP 整數範圍內'
            ],
            self::TYPE_FLOAT_CONVERSION_FAILED => [
                '確保值是有效的數字格式',
                '使用正確的小數點格式（如 3.14）',
                '避免使用科學記號法（除非必要）'
            ],
            self::TYPE_ARRAY_CONVERSION_FAILED => [
                '使用有效的 JSON 格式',
                '檢查陣列語法是否正確',
                '確認逗號和括號配對正確'
            ],
            self::TYPE_STRING_CONVERSION_FAILED => [
                '檢查值是否可以轉換為字串',
                '避免使用複雜的物件或資源'
            ],
            self::TYPE_UNSUPPORTED_TYPE => [
                '使用支援的型別：boolean, integer, float, string, array',
                '檢查配置定義中的型別設定'
            ],
            default => parent::getRepairSuggestions()
        };
    }

    /**
     * 獲取詳細的錯誤報告
     *
     * @return string 錯誤報告
     */
    public function getDetailedReport(): string
    {
        $report = [];
        $report[] = "=== 型別轉換錯誤報告 ===";
        $report[] = "配置鍵：" . $this->configKey;
        $report[] = "原始值：" . var_export($this->originalValue, true);
        $report[] = "原始型別：" . gettype($this->originalValue);
        $report[] = "目標型別：" . $this->targetType;
        $report[] = "錯誤訊息：" . $this->getMessage();
        $report[] = "錯誤碼：" . $this->getCode();
        $report[] = "";

        $suggestions = $this->getRepairSuggestions();
        if (!empty($suggestions)) {
            $report[] = "修復建議：";
            foreach ($suggestions as $suggestion) {
                $report[] = "  • " . $suggestion;
            }
        }

        return implode("\n", $report);
    }
}