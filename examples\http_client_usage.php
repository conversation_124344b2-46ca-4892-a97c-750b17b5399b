<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Fdmc\FileServiceClient\FileServiceClient;
use Fdmc\FileServiceClient\Http\GuzzleHttpClient;
use Fdmc\FileServiceClient\Http\HttpClientInterface;
use Fdmc\FileServiceClient\FileServiceException;

/**
 * HTTP 客戶端使用示例
 * 展示如何使用重構後的 FileServiceClient 和 HttpClient
 */

try {
    // 方式 1: 直接使用 FileServiceClient（推薦）
    echo "=== 使用 FileServiceClient ===\n";

    $client = new FileServiceClient('https://api.example.com', [
        'timeout' => 60,
        'verify' => true, // 生產環境建議啟用 SSL 驗證
    ]);

    // 設定自定義標頭
    $client->setHeaders([
        'Authorization' => 'Bearer your-token-here',
        'X-Custom-Header' => 'custom-value'
    ]);

    // 使用檔案服務功能
    $companyId = 1;
    $scope = 'documents';
    $userId = 123;

    // 列出根目錄物件
    $objects = $client->listRootObjects($companyId, $scope, $userId);
    echo "根目錄物件數量: " . count($objects['data'] ?? []) . "\n";

    // 方式 2: 直接使用 GuzzleHttpClient（進階使用，不推薦）
    echo "\n=== 使用 GuzzleHttpClient ===\n";

    $httpClient = new GuzzleHttpClient('https://api.example.com', [
        'timeout' => 30
    ]);

    // 設定標頭
    $httpClient->setHeaders([
        'Authorization' => 'Bearer your-token-here'
    ]);

    // 發送自定義 JSON 請求
    $response = $httpClient->sendJsonRequest('GET', '/custom-endpoint', [
        'query' => ['param1' => 'value1']
    ]);
    echo "自定義端點回應: " . json_encode($response) . "\n";

    // 發送原始請求（用於下載等）
    $rawResponse = $httpClient->sendRawRequest('GET', '/health-check');
    echo "健康檢查狀態碼: " . $rawResponse->getStatusCode() . "\n";

    // 方式 3: 從 FileServiceClient 獲取 HttpClient（進階使用）
    echo "\n=== 從 FileServiceClient 獲取 HttpClient ===\n";

    $fileClient = new FileServiceClient('https://api.example.com');
    $httpClient = $fileClient->getHttpClient();

    echo "基礎 URL: " . $httpClient->getBaseUrl() . "\n";
    echo "預設標頭: " . json_encode($httpClient->getDefaultHeaders()) . "\n";

    // 獲取底層的 Guzzle 客戶端（高級使用案例）
    $guzzleClient = $httpClient->getGuzzleClient();
    echo "Guzzle 客戶端類別: " . get_class($guzzleClient) . "\n";

} catch (FileServiceException $e) {
    echo "檔案服務錯誤: " . $e->getMessage() . "\n";
    echo "錯誤代碼: " . $e->getCode() . "\n";

    if ($e->getPrevious()) {
        echo "原始錯誤: " . $e->getPrevious()->getMessage() . "\n";
    }
} catch (Exception $e) {
    echo "一般錯誤: " . $e->getMessage() . "\n";
}

/**
 * HTTP 客戶端的優點：
 * 
 * 1. 關注點分離：
 *    - HttpClient 專門處理 HTTP 通訊
 *    - FileServiceClient 專注於檔案服務業務邏輯
 * 
 * 2. 重用性：
 *    - HttpClient 可以在其他項目中重用
 *    - 統一的 HTTP 錯誤處理和回應解析
 * 
 * 3. 可測試性：
 *    - 可以輕鬆模擬 HttpClient 進行單元測試
 *    - 業務邏輯與 HTTP 實現解耦
 * 
 * 4. 可擴展性：
 *    - 可以輕鬆添加新的 HTTP 功能（如重試、緩存等）
 *    - 支援不同類型的請求（JSON、原始內容等）
 * 
 * 5. 維護性：
 *    - HTTP 相關的變更只需要修改 HttpClient
 *    - 更清晰的代碼結構和職責劃分
 */