# 配置系統遷移指南

本指南說明如何從舊的 options 系統遷移到新的 Laravel 風格配置系統。

## 向後相容性

**好消息：新的配置系統完全向後相容！** 您的現有程式碼無需修改即可繼續使用。

## 遷移前後對比

### 舊的方式（仍然有效）

```php
use FDMC\FileServiceClient\FileServiceClient;

// 舊的方式：直接傳遞所有選項
$client = new FileServiceClient([
    'baseUrl' => 'https://api.example.com',
    'timeout' => 30,
    'connect_timeout' => 10,
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'Authorization' => 'Bearer token',
    ],
    'verify' => false,
]);
```

### 新的方式（推薦）

```php
use FDMC\FileServiceClient\FileServiceClient;

// 新的方式：配置檔案 + 選項覆蓋
$client = new FileServiceClient([
    'baseUrl' => 'https://api.example.com',
    'headers' => [
        'Authorization' => 'Bearer token', // 只需要覆蓋特定項目
    ],
]);
```

## 遷移步驟

### 步驟 1：檢查現有配置

檢查您目前傳遞給 `FileServiceClient` 的選項：

```php
$options = [
    'baseUrl' => 'https://api.example.com',
    'timeout' => 30,
    'connect_timeout' => 10,
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'Authorization' => 'Bearer token',
    ],
    'verify' => false,
];

$client = new FileServiceClient($options);
```

### 步驟 2：識別可以移到配置檔案的選項

將常見的、不經常變動的配置移到 `config/file-service.php`：

```php
// config/file-service.php
return [
    'baseUrl' => env('FILE_SERVICE_BASE_URL', 'https://api.example.com'),
    'http' => [
        'timeout' => env('FILE_SERVICE_TIMEOUT', 30),
        'connect_timeout' => env('FILE_SERVICE_CONNECT_TIMEOUT', 10),
        'verify' => env('FILE_SERVICE_SSL_VERIFY', false),
    ],
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
    ],
];
```

### 步驟 3：簡化程式碼中的選項

只保留需要動態變更的選項：

```php
// 簡化後的程式碼
$client = new FileServiceClient([
    'headers' => [
        'Authorization' => 'Bearer ' . $userToken, // 動態值
    ],
]);
```

### 步驟 4：使用環境變數

將環境相關的配置移到環境變數：

```bash
# .env 檔案
FILE_SERVICE_BASE_URL=https://api.example.com
FILE_SERVICE_TIMEOUT=30
FILE_SERVICE_SSL_VERIFY=false
```

## 遷移範例

### 範例 1：基本 API 客戶端

**遷移前：**
```php
$client = new FileServiceClient([
    'baseUrl' => 'https://api.example.com',
    'timeout' => 30,
    'connect_timeout' => 10,
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
    ],
]);
```

**遷移後：**
```php
// 配置檔案中設置預設值
// config/file-service.php
return [
    'baseUrl' => env('FILE_SERVICE_BASE_URL', 'https://api.example.com'),
    'http' => [
        'timeout' => 30,
        'connect_timeout' => 10,
    ],
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
    ],
];

// 程式碼簡化為
$client = new FileServiceClient(); // 使用預設配置
```

### 範例 2：多環境配置

**遷移前：**
```php
$baseUrl = $_ENV['APP_ENV'] === 'production' 
    ? 'https://api.prod.com' 
    : 'https://api.dev.com';

$client = new FileServiceClient([
    'baseUrl' => $baseUrl,
    'timeout' => $_ENV['APP_ENV'] === 'production' ? 60 : 30,
    'verify' => $_ENV['APP_ENV'] === 'production',
]);
```

**遷移後：**
```php
// 使用環境變數
// .env.production
FILE_SERVICE_BASE_URL=https://api.prod.com
FILE_SERVICE_TIMEOUT=60
FILE_SERVICE_SSL_VERIFY=true

// .env.development
FILE_SERVICE_BASE_URL=https://api.dev.com
FILE_SERVICE_TIMEOUT=30
FILE_SERVICE_SSL_VERIFY=false

// 程式碼簡化為
$client = new FileServiceClient(); // 自動根據環境變數配置
```

### 範例 3：動態認證

**遷移前：**
```php
$client = new FileServiceClient([
    'baseUrl' => 'https://api.example.com',
    'timeout' => 30,
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'Authorization' => 'Bearer ' . $userToken,
        'X-User-ID' => $userId,
    ],
]);
```

**遷移後：**
```php
// 只覆蓋動態部分
$client = new FileServiceClient([
    'headers' => [
        'Authorization' => 'Bearer ' . $userToken,
        'X-User-ID' => $userId,
    ],
]);
```

## 遷移檢查清單

- [ ] 檢查現有的 `FileServiceClient` 建構子調用
- [ ] 識別可以移到配置檔案的靜態配置
- [ ] 識別可以使用環境變數的配置
- [ ] 更新配置檔案 `config/file-service.php`
- [ ] 設置相關的環境變數
- [ ] 簡化程式碼中的選項陣列
- [ ] 測試遷移後的功能是否正常

## 常見問題

### Q: 我需要立即遷移嗎？
A: 不需要。新系統完全向後相容，您可以按自己的節奏逐步遷移。

### Q: 如何確保遷移後功能正常？
A: 執行現有的測試套件，確保所有功能都正常運作。

### Q: 可以混合使用舊新方式嗎？
A: 可以。您可以在配置檔案中設置預設值，同時在程式碼中覆蓋特定選項。

### Q: 如何處理敏感資訊？
A: 使用環境變數存儲敏感資訊，如 API 金鑰、密碼等。

## 遷移的好處

1. **程式碼更簡潔** - 減少重複的配置程式碼
2. **更好的維護性** - 集中管理配置
3. **環境友好** - 輕鬆處理不同環境的配置
4. **更安全** - 敏感資訊通過環境變數管理
5. **更靈活** - 支援配置優先級和合併邏輯

## 需要幫助？

如果在遷移過程中遇到問題，請參考：
- [配置系統說明](CONFIG_SYSTEM.md)
- [使用範例](examples/config_usage_example.php)
- [單元測試](tests/ConfigManagerTest.php)