#!/usr/bin/env php
<?php

declare(strict_types=1);

/**
 * File Service Client 配置發布工具
 * 
 * 用於將套件預設配置檔案發布到原生 PHP 專案中
 * 
 * 使用方法：
 *   php bin/publish-config.php [選項]
 * 
 * 選項：
 *   -t, --target PATH    指定目標配置檔案路徑
 *   -f, --force          強制覆蓋現有檔案
 *   -h, --help           顯示幫助資訊
 */

// 自動載入
$autoloadPaths = [
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../../autoload.php',
    __DIR__ . '/../../../../autoload.php',
];

$autoloadFound = false;
foreach ($autoloadPaths as $autoloadPath) {
    if (file_exists($autoloadPath)) {
        require_once $autoloadPath;
        $autoloadFound = true;
        break;
    }
}

if (!$autoloadFound) {
    echo "錯誤：找不到 Composer autoload 檔案。請執行 'composer install'。\n";
    exit(1);
}

use FDMC\FileServiceClient\Commands\PublishConfigCommand;

// 檢查 PHP 版本
if (version_compare(PHP_VERSION, '8.0.0', '<')) {
    echo "錯誤：此工具需要 PHP 8.0 或更高版本。當前版本：" . PHP_VERSION . "\n";
    exit(1);
}

// 檢查是否在命令列環境中執行
if (php_sapi_name() !== 'cli') {
    echo "錯誤：此工具只能在命令列環境中執行。\n";
    exit(1);
}

try {
    // 建立並執行命令
    $command = new PublishConfigCommand();
    $exitCode = $command->execute(array_slice($argv, 1));
    exit($exitCode);
    
} catch (\Throwable $e) {
    echo "嚴重錯誤：" . $e->getMessage() . "\n";
    echo "檔案：" . $e->getFile() . ":" . $e->getLine() . "\n";
    
    if (getenv('DEBUG') === 'true') {
        echo "\n堆疊追蹤：\n" . $e->getTraceAsString() . "\n";
    }
    
    exit(1);
}