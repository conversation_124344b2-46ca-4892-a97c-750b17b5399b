<?php

namespace FDMC\FileServiceClient\Core;

use FDMC\FileServiceClient\Exceptions\FileServiceException;

/**
 * 配置載入器
 * 負責從各種來源載入配置檔案，包括搜尋、驗證和合併
 */
class ConfigLoader
{
    /**
     * 自訂配置檔案路徑
     */
    private ?string $customPath = null;
    /**
     * 設定自訂配置檔案路徑
     * 
     * @param string $path 配置檔案路徑
     * @return void
     */
    public function setCustomPath(string $path): void
    {
        $this->customPath = $path;
    }

    /**
     * 獲取自訂配置檔案路徑
     * 
     * @return string|null
     */
    public function getCustomPath(): ?string
    {
        return $this->customPath;
    }

    /**
     * 從檔案載入配置
     * 
     * @param string|null $customPath 自訂配置檔案路徑
     * @return array 載入的配置
     * @throws FileServiceException 配置載入失敗時拋出例外
     */
    public function loadFromFile(?string $customPath = null): array
    {
        // 優先級：方法參數 > 設定的自訂路徑 > 自動搜尋
        $targetPath = $customPath ?? $this->customPath;

        // 如果指定了自訂路徑，直接使用該路徑
        if ($targetPath !== null) {
            if (!file_exists($targetPath)) {
                // 明確指定的路徑不存在時，使用預設配置
                return $this->getDefaultConfig();
            }

            try {
                // 檢查檔案權限和可讀性
                $this->validateConfigFile($targetPath);

                // 載入配置檔案
                $config = require $targetPath;

                // 驗證配置格式
                $this->validateConfig($config, $targetPath);

                // 與預設配置合併
                return $this->mergeWithDefaults($config);

            } catch (\Exception $e) {
                if ($e instanceof FileServiceException) {
                    throw $e;
                }
                throw new FileServiceException("載入配置檔案失敗: {$e->getMessage()}", 0, $e);
            }
        }

        // 自動搜尋配置檔案
        $configPath = $this->findConfigFile();

        if ($configPath === null) {
            // 配置檔案不存在時，使用預設配置
            return $this->getDefaultConfig();
        }

        try {
            // 檢查檔案權限和可讀性
            $this->validateConfigFile($configPath);

            // 載入配置檔案
            $config = require $configPath;

            // 驗證配置格式
            $this->validateConfig($config, $configPath);

            // 與預設配置合併
            return $this->mergeWithDefaults($config);

        } catch (\Exception $e) {
            if ($e instanceof FileServiceException) {
                throw $e;
            }
            throw new FileServiceException("載入配置檔案失敗: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 搜尋配置檔案
     * 使用改進的搜尋邏輯，基於專案根目錄偵測
     * 
     * @return string|null 找到的配置檔案路徑
     */
    public function findConfigFile(): ?string
    {
        $searchPaths = $this->getConfigSearchPaths();

        foreach ($searchPaths as $path) {
            if ($this->isValidConfigFile($path)) {
                return $path;
            }
        }

        return null;
    }

    /**
     * 檢查檔案是否為有效的配置檔案
     * 包含權限、可讀性和基本格式檢查
     * 
     * @param string $path 檔案路徑
     * @return bool 是否為有效的配置檔案
     */
    private function isValidConfigFile(string $path): bool
    {
        // 基本存在性檢查
        if (!file_exists($path)) {
            return false;
        }

        // 檢查是否為檔案（不是目錄或其他類型）
        if (!is_file($path)) {
            return false;
        }

        // 檢查檔案權限和可讀性
        if (!is_readable($path)) {
            return false;
        }

        // 檢查檔案大小（避免載入過大的檔案）
        $fileSize = filesize($path);
        if ($fileSize === false || $fileSize > 1024 * 1024) { // 1MB 限制
            return false;
        }

        // 檢查檔案副檔名
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        if (strtolower($extension) !== 'php') {
            return false;
        }

        return true;
    }

    /**
     * 獲取配置檔案搜尋路徑
     * 改進的搜尋順序，基於專案根目錄自動偵測
     * 
     * @return array 搜尋路徑陣列
     */
    public function getConfigSearchPaths(): array
    {
        $paths = [];

        // 1. 專案根目錄（基於 composer.json）
        $projectRoot = $this->findProjectRoot();
        if ($projectRoot) {
            $paths[] = $projectRoot . DIRECTORY_SEPARATOR . 'config' . DIRECTORY_SEPARATOR . 'file-service.php';
        }

        // 2. 當前工作目錄
        $cwd = getcwd();
        if ($cwd !== false) {
            $paths[] = $cwd . DIRECTORY_SEPARATOR . 'config' . DIRECTORY_SEPARATOR . 'file-service.php';
        }

        // 3. 相對於入口檔案的路徑
        if (isset($_SERVER['SCRIPT_FILENAME']) && !empty($_SERVER['SCRIPT_FILENAME'])) {
            $scriptDir = dirname($_SERVER['SCRIPT_FILENAME']);
            $realScriptDir = realpath($scriptDir);
            if ($realScriptDir !== false) {
                $paths[] = $realScriptDir . DIRECTORY_SEPARATOR . 'config' . DIRECTORY_SEPARATOR . 'file-service.php';
            }
        }

        // 4. 套件預設配置路徑
        $packageConfigPath = __DIR__ . '/../config/file-service.php';
        $realPackageConfigPath = realpath($packageConfigPath);
        if ($realPackageConfigPath !== false) {
            $paths[] = $realPackageConfigPath;
        } else {
            // 如果 realpath 失敗，仍然加入原始路徑作為備用
            $paths[] = $packageConfigPath;
        }

        // 移除重複路徑並過濾無效路徑
        $uniquePaths = array_unique(array_filter($paths, function ($path) {
            return !empty($path) && is_string($path);
        }));

        return array_values($uniquePaths);
    }

    /**
     * 尋找專案根目錄（基於 composer.json）
     * 改進版：處理符號連結和相對路徑問題
     * 
     * @return string|null 專案根目錄路徑
     */
    public function findProjectRoot(): ?string
    {
        // 從多個起始點開始搜尋
        $startingPoints = [
            getcwd(), // 當前工作目錄
            dirname($_SERVER['SCRIPT_FILENAME'] ?? ''), // 入口檔案目錄
            __DIR__, // 當前類別檔案目錄
        ];

        foreach ($startingPoints as $startDir) {
            if (empty($startDir) || !is_dir($startDir)) {
                continue;
            }

            // 解析符號連結和相對路徑
            $realStartDir = realpath($startDir);
            if ($realStartDir === false) {
                continue;
            }

            $projectRoot = $this->searchProjectRootFromDirectory($realStartDir);
            if ($projectRoot !== null) {
                return $projectRoot;
            }
        }

        return null;
    }

    /**
     * 從指定目錄開始向上搜尋專案根目錄
     * 
     * @param string $startDir 起始目錄
     * @return string|null 專案根目錄路徑
     */
    private function searchProjectRootFromDirectory(string $startDir): ?string
    {
        $currentDir = $startDir;
        $maxDepth = 10; // 最多向上搜尋 10 層目錄

        for ($i = 0; $i < $maxDepth; $i++) {
            $composerPath = $currentDir . DIRECTORY_SEPARATOR . 'composer.json';

            if (file_exists($composerPath) && is_readable($composerPath)) {
                // 驗證這是一個有效的 composer.json 檔案
                if ($this->isValidComposerJson($composerPath)) {
                    return $currentDir;
                }
            }

            $parentDir = dirname($currentDir);
            if ($parentDir === $currentDir) {
                break; // 已到達根目錄
            }

            $currentDir = $parentDir;
        }

        return null;
    }

    /**
     * 驗證 composer.json 檔案是否有效
     * 
     * @param string $composerPath composer.json 檔案路徑
     * @return bool 是否為有效的 composer.json
     */
    private function isValidComposerJson(string $composerPath): bool
    {
        try {
            $content = file_get_contents($composerPath);
            if ($content === false) {
                return false;
            }

            $json = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return false;
            }

            // 檢查是否包含基本的 composer.json 結構
            return is_array($json) && (
                isset($json['name']) ||
                isset($json['require']) ||
                isset($json['autoload']) ||
                isset($json['require-dev'])
            );
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 驗證配置檔案
     * 包含完整的檔案權限、可讀性和安全性檢查
     * 
     * @param string $configPath 配置檔案路徑
     * @throws FileServiceException 驗證失敗時拋出例外
     */
    public function validateConfigFile(string $configPath): void
    {
        if (!file_exists($configPath)) {
            throw new FileServiceException("配置檔案不存在: {$configPath}");
        }

        if (!is_file($configPath)) {
            throw new FileServiceException("配置路徑不是檔案: {$configPath}");
        }

        if (!is_readable($configPath)) {
            $permissions = substr(sprintf('%o', fileperms($configPath)), -4);
            throw new FileServiceException("配置檔案無法讀取，請檢查檔案權限 ({$permissions}): {$configPath}");
        }

        // 檢查檔案大小
        $fileSize = filesize($configPath);
        if ($fileSize === false) {
            throw new FileServiceException("無法獲取配置檔案大小: {$configPath}");
        }

        if ($fileSize > 1024 * 1024) { // 1MB 限制
            throw new FileServiceException("配置檔案過大 ({$fileSize} bytes)，最大允許 1MB: {$configPath}");
        }

        if ($fileSize === 0) {
            throw new FileServiceException("配置檔案為空: {$configPath}");
        }

        // 檢查檔案副檔名
        $extension = pathinfo($configPath, PATHINFO_EXTENSION);
        if (strtolower($extension) !== 'php') {
            throw new FileServiceException("配置檔案必須為 PHP 檔案 (.php): {$configPath}");
        }

        // 檢查檔案是否可能包含 PHP 語法錯誤
        $this->validatePhpSyntax($configPath);
    }

    /**
     * 驗證 PHP 檔案語法
     * 
     * @param string $configPath 配置檔案路徑
     * @throws FileServiceException 語法錯誤時拋出例外
     */
    private function validatePhpSyntax(string $configPath): void
    {
        $content = file_get_contents($configPath);
        if ($content === false) {
            throw new FileServiceException("無法讀取配置檔案內容: {$configPath}");
        }

        // 基本的 PHP 開始標籤檢查
        if (!str_contains($content, '<?php')) {
            throw new FileServiceException("配置檔案缺少 PHP 開始標籤: {$configPath}");
        }

        // 使用 php -l 檢查語法（如果可用）
        if (function_exists('exec') && !empty(shell_exec('which php'))) {
            $output = [];
            $returnCode = 0;
            exec("php -l " . escapeshellarg($configPath) . " 2>&1", $output, $returnCode);

            if ($returnCode !== 0) {
                $errorMessage = implode("\n", $output);
                throw new FileServiceException("配置檔案語法錯誤: {$errorMessage}");
            }
        }
    }

    /**
     * 驗證配置格式和必要欄位
     * 包含完整的配置結構和數值範圍驗證
     * 
     * @param mixed $config 配置資料
     * @param string $configPath 配置檔案路徑
     * @throws FileServiceException 驗證失敗時拋出例外
     */
    public function validateConfig($config, string $configPath): void
    {
        if (!is_array($config)) {
            throw new FileServiceException("配置檔案必須返回陣列格式: {$configPath}");
        }

        // 驗證基本結構
        $this->validateRequiredFields($config, $configPath);

        // 驗證 URL 格式
        $this->validateUrlFields($config, $configPath);

        // 驗證數值型別和範圍
        $this->validateNumericFields($config, $configPath);

        // 驗證布林值
        $this->validateBooleanFields($config, $configPath);

        // 驗證枚舉值
        $this->validateEnumFields($config, $configPath);

        // 驗證陣列結構
        $this->validateArrayFields($config, $configPath);
    }

    /**
     * 驗證必要欄位
     */
    private function validateRequiredFields(array $config, string $configPath): void
    {
        $requiredKeys = ['baseUrl'];
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key])) {
                throw new FileServiceException("配置檔案缺少必要欄位 '{$key}': {$configPath}");
            }
        }
    }

    /**
     * 驗證 URL 格式欄位
     */
    private function validateUrlFields(array $config, string $configPath): void
    {
        $urlFields = ['baseUrl'];

        foreach ($urlFields as $field) {
            $value = $this->getNestedValue($config, $field);
            if ($value !== null && !empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                throw new FileServiceException("配置檔案中的 '{$field}' 格式不正確，必須為有效的 URL: {$configPath}");
            }
        }
    }

    /**
     * 驗證數值型別和範圍
     */
    private function validateNumericFields(array $config, string $configPath): void
    {
        $numericFields = [
            'http.timeout' => ['min' => 1, 'max' => 300],
            'http.connect_timeout' => ['min' => 1, 'max' => 60],
            'http.read_timeout' => ['min' => 1, 'max' => 600],
            'upload.timeout' => ['min' => 1, 'max' => 3600],
            'upload.max_file_size' => ['min' => 1, 'max' => PHP_INT_MAX],
            'download.timeout' => ['min' => 1, 'max' => 3600],
            'download.stream_buffer_size' => ['min' => 1024, 'max' => 1048576],
            'download.large_file_threshold' => ['min' => 1, 'max' => PHP_INT_MAX],
            'cache.ttl' => ['min' => 1, 'max' => 86400],
            'retry.max_attempts' => ['min' => 1, 'max' => 10],
            'retry.delay' => ['min' => 100, 'max' => 10000],
            'retry.multiplier' => ['min' => 1, 'max' => 10],
        ];

        foreach ($numericFields as $field => $constraints) {
            $value = $this->getNestedValue($config, $field);
            if ($value !== null) {
                if (!is_numeric($value)) {
                    throw new FileServiceException("配置檔案中的 '{$field}' 必須為數值: {$configPath}");
                }

                $numValue = (float) $value;
                if (isset($constraints['min']) && $numValue < $constraints['min']) {
                    throw new FileServiceException("配置檔案中的 '{$field}' 值 ({$numValue}) 小於最小值 ({$constraints['min']}): {$configPath}");
                }

                if (isset($constraints['max']) && $numValue > $constraints['max']) {
                    throw new FileServiceException("配置檔案中的 '{$field}' 值 ({$numValue}) 大於最大值 ({$constraints['max']}): {$configPath}");
                }
            }
        }
    }

    /**
     * 驗證布林值欄位
     */
    private function validateBooleanFields(array $config, string $configPath): void
    {
        $booleanFields = [
            'http.verify',
            'http.http_errors',
            'cache.enabled',
            'logging.enabled',
        ];

        foreach ($booleanFields as $field) {
            $value = $this->getNestedValue($config, $field);
            if ($value !== null && !is_bool($value)) {
                throw new FileServiceException("配置檔案中的 '{$field}' 必須為布林值 (true/false): {$configPath}");
            }
        }
    }

    /**
     * 驗證枚舉值欄位
     */
    private function validateEnumFields(array $config, string $configPath): void
    {
        $enumFields = [
            'cache.driver' => ['file', 'redis', 'memcached', 'array'],
            'logging.level' => ['debug', 'info', 'notice', 'warning', 'error', 'critical', 'alert', 'emergency'],
        ];

        foreach ($enumFields as $field => $allowedValues) {
            $value = $this->getNestedValue($config, $field);
            if ($value !== null && !in_array($value, $allowedValues, true)) {
                $allowedStr = implode(', ', $allowedValues);
                throw new FileServiceException("配置檔案中的 '{$field}' 值 '{$value}' 不在允許的值範圍內 ({$allowedStr}): {$configPath}");
            }
        }
    }

    /**
     * 驗證陣列結構欄位
     */
    private function validateArrayFields(array $config, string $configPath): void
    {
        $arrayFields = [
            'headers',
            'upload.allowed_mime_types',
        ];

        foreach ($arrayFields as $field) {
            $value = $this->getNestedValue($config, $field);
            if ($value !== null && !is_array($value)) {
                throw new FileServiceException("配置檔案中的 '{$field}' 必須為陣列: {$configPath}");
            }
        }

        // 驗證 MIME 類型格式
        $mimeTypes = $this->getNestedValue($config, 'upload.allowed_mime_types');
        if (is_array($mimeTypes)) {
            foreach ($mimeTypes as $mimeType) {
                if (!is_string($mimeType) || !preg_match('/^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*$/', $mimeType)) {
                    throw new FileServiceException("配置檔案中的 MIME 類型格式不正確: '{$mimeType}': {$configPath}");
                }
            }
        }
    }

    /**
     * 與預設配置合併
     * 
     * @param array $config 使用者配置
     * @return array 合併後的配置
     */
    public function mergeWithDefaults(array $config): array
    {
        $defaultConfig = $this->getDefaultConfig();
        return $this->arrayMergeRecursive($defaultConfig, $config);
    }

    /**
     * 獲取預設配置
     * 
     * @return array 預設配置
     */
    private function getDefaultConfig(): array
    {
        return [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'FDMC-FileServiceClient/1.0',
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600, // 100MB
                'allowed_mime_types' => [
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                    'image/webp',
                    'application/pdf',
                    'text/plain',
                    'application/json',
                    'application/zip',
                    'application/x-zip-compressed',
                ],
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800, // 50MB
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2,
            ],
        ];
    }

    /**
     * 獲取巢狀陣列的值
     * 
     * @param array $array 陣列
     * @param string $key 鍵，支援點號分隔
     * @param mixed $default 預設值
     * @return mixed 值
     */
    private function getNestedValue(array $array, string $key, $default = null)
    {
        if (strpos($key, '.') === false) {
            return $array[$key] ?? $default;
        }

        $keys = explode('.', $key);
        $value = $array;

        foreach ($keys as $nestedKey) {
            if (!is_array($value) || !array_key_exists($nestedKey, $value)) {
                return $default;
            }
            $value = $value[$nestedKey];
        }

        return $value;
    }

    /**
     * 遞迴合併陣列
     * 
     * @param array $array1 第一個陣列
     * @param array $array2 第二個陣列
     * @return array 合併後的陣列
     */
    private function arrayMergeRecursive(array $array1, array $array2): array
    {
        $merged = $array1;

        foreach ($array2 as $key => $value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = $this->arrayMergeRecursive($merged[$key], $value);
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }
}