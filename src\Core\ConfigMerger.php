<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Core;

/**
 * 配置合併器
 * 
 * 負責處理配置的合併邏輯，包括：
 * - 遞迴陣列合併
 * - 配置優先級處理
 * - 使用者選項與預設配置合併
 * - 環境變數覆蓋機制
 */
class ConfigMerger
{
    /**
     * 配置來源優先級（數字越大優先級越高）
     */
    public const PRIORITY_DEFAULT = 1;          // 套件預設配置
    public const PRIORITY_PROJECT_CONFIG = 2;   // 專案配置檔案
    public const PRIORITY_ENVIRONMENT = 3;      // 環境變數
    public const PRIORITY_USER_OPTIONS = 4;     // 使用者選項
    public const PRIORITY_RUNTIME = 5;          // 動態設定

    /**
     * 合併多個配置來源
     * 
     * @param array $configs 配置陣列，格式：['priority' => int, 'config' => array]
     * @return array 合併後的配置
     */
    public function mergeConfigs(array $configs): array
    {
        // 按優先級排序（低優先級在前）
        usort($configs, function ($a, $b) {
            return ($a['priority'] ?? 0) <=> ($b['priority'] ?? 0);
        });

        $merged = [];
        foreach ($configs as $configData) {
            if (!isset($configData['config']) || !is_array($configData['config'])) {
                continue;
            }

            $merged = $this->mergeRecursive($merged, $configData['config']);
        }

        return $merged;
    }

    /**
     * 遞迴合併兩個配置陣列
     * 
     * @param array $base 基礎配置
     * @param array $override 覆蓋配置
     * @return array 合併後的配置
     */
    public function mergeRecursive(array $base, array $override): array
    {
        $merged = $base;

        foreach ($override as $key => $value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                // 遞迴合併巢狀陣列
                $merged[$key] = $this->mergeRecursive($merged[$key], $value);
            } else {
                // 直接覆蓋值（使用者選項優先）
                $merged[$key] = $value;
            }
        }

        return $merged;
    }

    /**
     * 合併使用者選項與預設配置
     * 
     * @param array $defaultConfig 預設配置
     * @param array $userOptions 使用者選項
     * @return array 合併後的配置
     */
    public function mergeUserOptions(array $defaultConfig, array $userOptions): array
    {
        return $this->mergeConfigs([
            ['priority' => self::PRIORITY_DEFAULT, 'config' => $defaultConfig],
            ['priority' => self::PRIORITY_USER_OPTIONS, 'config' => $userOptions],
        ]);
    }

    /**
     * 應用環境變數覆蓋
     * 
     * @param array $config 基礎配置
     * @param array $envOverrides 環境變數覆蓋
     * @return array 應用覆蓋後的配置
     */
    public function applyEnvironmentOverrides(array $config, array $envOverrides): array
    {
        return $this->mergeConfigs([
            ['priority' => self::PRIORITY_PROJECT_CONFIG, 'config' => $config],
            ['priority' => self::PRIORITY_ENVIRONMENT, 'config' => $envOverrides],
        ]);
    }

    /**
     * 完整的配置合併流程
     * 
     * @param array $defaultConfig 預設配置
     * @param array $projectConfig 專案配置檔案
     * @param array $envOverrides 環境變數覆蓋
     * @param array $userOptions 使用者選項
     * @param array $runtimeConfig 運行時配置
     * @return array 最終合併的配置
     */
    public function mergeAllConfigs(
        array $defaultConfig = [],
        array $projectConfig = [],
        array $envOverrides = [],
        array $userOptions = [],
        array $runtimeConfig = []
    ): array {
        $configs = [];

        if (!empty($defaultConfig)) {
            $configs[] = ['priority' => self::PRIORITY_DEFAULT, 'config' => $defaultConfig];
        }

        if (!empty($projectConfig)) {
            $configs[] = ['priority' => self::PRIORITY_PROJECT_CONFIG, 'config' => $projectConfig];
        }

        if (!empty($envOverrides)) {
            $configs[] = ['priority' => self::PRIORITY_ENVIRONMENT, 'config' => $envOverrides];
        }

        if (!empty($userOptions)) {
            $configs[] = ['priority' => self::PRIORITY_USER_OPTIONS, 'config' => $userOptions];
        }

        if (!empty($runtimeConfig)) {
            $configs[] = ['priority' => self::PRIORITY_RUNTIME, 'config' => $runtimeConfig];
        }

        return $this->mergeConfigs($configs);
    }

    /**
     * 智慧陣列合併
     * 處理特殊的陣列合併情況
     * 
     * @param array $base 基礎陣列
     * @param array $override 覆蓋陣列
     * @param string $key 當前處理的鍵
     * @return array 合併後的陣列
     */
    public function smartArrayMerge(array $base, array $override, string $key = ''): array
    {
        // 對於某些特殊鍵，使用完全替換而不是合併
        $replaceKeys = [
            'headers',
            'upload.allowed_mime_types',
        ];

        if (in_array($key, $replaceKeys, true)) {
            return $override;
        }

        // 檢查是否為索引陣列
        if ($this->isIndexedArray($base) && $this->isIndexedArray($override)) {
            // 索引陣列：合併並去重
            return array_values(array_unique(array_merge($base, $override)));
        }

        // 關聯陣列：遞迴合併
        return $this->mergeRecursive($base, $override);
    }

    /**
     * 檢查陣列是否為索引陣列
     * 
     * @param array $array 要檢查的陣列
     * @return bool 是否為索引陣列
     */
    public function isIndexedArray(array $array): bool
    {
        if (empty($array)) {
            return true;
        }

        return array_keys($array) === range(0, count($array) - 1);
    }

    /**
     * 深度複製配置陣列
     * 避免引用問題
     * 
     * @param array $config 要複製的配置
     * @return array 複製後的配置
     */
    public function deepCopy(array $config): array
    {
        $copy = [];

        foreach ($config as $key => $value) {
            if (is_array($value)) {
                $copy[$key] = $this->deepCopy($value);
            } else {
                $copy[$key] = $value;
            }
        }

        return $copy;
    }

    /**
     * 驗證配置合併結果
     * 
     * @param array $config 合併後的配置
     * @return bool 是否有效
     */
    public function validateMergedConfig(array $config): bool
    {
        // 檢查必要的配置項目
        $requiredKeys = ['baseUrl'];

        foreach ($requiredKeys as $key) {
            if (!$this->hasNestedKey($config, $key)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 檢查巢狀鍵是否存在
     * 
     * @param array $array 陣列
     * @param string $key 鍵，支援點號分隔
     * @return bool 是否存在
     */
    public function hasNestedKey(array $array, string $key): bool
    {
        if (strpos($key, '.') === false) {
            return array_key_exists($key, $array);
        }

        $keys = explode('.', $key);
        $current = $array;

        foreach ($keys as $nestedKey) {
            if (!is_array($current) || !array_key_exists($nestedKey, $current)) {
                return false;
            }
            $current = $current[$nestedKey];
        }

        return true;
    }

    /**
     * 獲取配置差異
     * 用於偵錯和日誌記錄
     * 
     * @param array $before 合併前的配置
     * @param array $after 合併後的配置
     * @return array 差異陣列
     */
    public function getConfigDiff(array $before, array $after): array
    {
        $diff = [];

        // 檢查新增和修改的項目
        foreach ($after as $key => $value) {
            if (!array_key_exists($key, $before)) {
                $diff['added'][$key] = $value;
            } elseif ($before[$key] !== $value) {
                if (is_array($value) && is_array($before[$key])) {
                    $nestedDiff = $this->getConfigDiff($before[$key], $value);
                    if (!empty($nestedDiff)) {
                        $diff['modified'][$key] = $nestedDiff;
                    }
                } else {
                    $diff['modified'][$key] = [
                        'from' => $before[$key],
                        'to' => $value
                    ];
                }
            }
        }

        // 檢查刪除的項目
        foreach ($before as $key => $value) {
            if (!array_key_exists($key, $after)) {
                $diff['removed'][$key] = $value;
            }
        }

        return $diff;
    }
}