<?php

/**
 * File Service Client 全域配置函數使用範例
 * 
 * 展示如何使用 file_service_config() 函數及其相關輔助函數
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../src/helpers.php';

use FDMC\FileServiceClient\ConfigurationException;

echo "=== File Service Client 全域配置函數範例 ===\n\n";

try {
    // 1. 基本配置獲取
    echo "1. 基本配置獲取\n";
    echo "   baseUrl: " . file_service_config('baseUrl') . "\n";
    echo "   http.timeout: " . file_service_config('http.timeout') . "\n";
    echo "   不存在的配置（使用預設值）: " . file_service_config('non.existent', 'default_value') . "\n\n";

    // 2. 獲取所有配置
    echo "2. 獲取所有配置\n";
    $allConfig = file_service_config();
    echo "   配置項目數量: " . count($allConfig) . "\n";
    echo "   主要配置鍵: " . implode(', ', array_keys($allConfig)) . "\n\n";

    // 3. 動態設定配置
    echo "3. 動態設定配置\n";
    file_service_config_set('custom.api_key', 'my-secret-key');
    file_service_config_set('custom.debug', true);
    file_service_config_set('custom.nested.value', 'nested_data');
    
    echo "   custom.api_key: " . file_service_config('custom.api_key') . "\n";
    echo "   custom.debug: " . (file_service_config('custom.debug') ? 'true' : 'false') . "\n";
    echo "   custom.nested.value: " . file_service_config('custom.nested.value') . "\n\n";

    // 4. 批量設定配置
    echo "4. 批量設定配置\n";
    file_service_config([
        'batch.setting1' => 'value1',
        'batch.setting2' => 'value2',
        'batch.nested.deep' => 'deep_value'
    ]);
    
    echo "   batch.setting1: " . file_service_config('batch.setting1') . "\n";
    echo "   batch.setting2: " . file_service_config('batch.setting2') . "\n";
    echo "   batch.nested.deep: " . file_service_config('batch.nested.deep') . "\n\n";

    // 5. 配置存在檢查
    echo "5. 配置存在檢查\n";
    echo "   baseUrl 存在: " . (file_service_config_has('baseUrl') ? 'true' : 'false') . "\n";
    echo "   custom.api_key 存在: " . (file_service_config_has('custom.api_key') ? 'true' : 'false') . "\n";
    echo "   non.existent 存在: " . (file_service_config_has('non.existent') ? 'true' : 'false') . "\n\n";

    // 6. 點號語法的巢狀存取
    echo "6. 點號語法的巢狀存取\n";
    file_service_config_set('deep.level1.level2.level3', 'very_deep_value');
    
    $level1 = file_service_config('deep.level1');
    echo "   deep.level1 是陣列: " . (is_array($level1) ? 'true' : 'false') . "\n";
    echo "   deep.level1.level2.level3: " . file_service_config('deep.level1.level2.level3') . "\n\n";

    // 7. 配置覆蓋
    echo "7. 配置覆蓋\n";
    file_service_config_set('override.test', 'original_value');
    echo "   原始值: " . file_service_config('override.test') . "\n";
    
    file_service_config_set('override.test', 'new_value');
    echo "   覆蓋後: " . file_service_config('override.test') . "\n\n";

    // 8. 配置移除
    echo "8. 配置移除\n";
    file_service_config_set('will.be.removed', 'temporary_value');
    echo "   移除前: " . file_service_config('will.be.removed') . "\n";
    
    file_service_config_forget('will.be.removed');
    echo "   移除後: " . (file_service_config('will.be.removed') ?? 'null') . "\n\n";

    // 9. 使用 file_service_config_all() 獲取所有配置
    echo "9. 獲取所有配置（包含動態設定的）\n";
    $allConfigWithCustom = file_service_config_all();
    $customKeys = array_filter(array_keys($allConfigWithCustom), function($key) {
        return strpos($key, 'custom') === 0 || strpos($key, 'batch') === 0 || strpos($key, 'deep') === 0;
    });
    echo "   自訂配置鍵: " . implode(', ', $customKeys) . "\n\n";

    // 10. 複雜的配置結構
    echo "10. 複雜的配置結構\n";
    file_service_config_set('complex.database.host', 'localhost');
    file_service_config_set('complex.database.port', 3306);
    file_service_config_set('complex.database.credentials.username', 'admin');
    file_service_config_set('complex.database.credentials.password', 'secret');
    
    $database = file_service_config('complex.database');
    echo "   資料庫配置結構:\n";
    echo "   - host: " . $database['host'] . "\n";
    echo "   - port: " . $database['port'] . "\n";
    echo "   - username: " . $database['credentials']['username'] . "\n";
    echo "   - password: " . str_repeat('*', strlen($database['credentials']['password'])) . "\n\n";

} catch (ConfigurationException $e) {
    echo "配置錯誤: " . $e->getMessage() . "\n";
    echo "錯誤碼: " . $e->getCode() . "\n";
    echo "錯誤描述: " . $e->getErrorDescription() . "\n";
    
    $suggestions = $e->getRepairSuggestions();
    if (!empty($suggestions)) {
        echo "修復建議:\n";
        foreach ($suggestions as $suggestion) {
            echo "  • " . $suggestion . "\n";
        }
    }
} catch (Exception $e) {
    echo "未預期的錯誤: " . $e->getMessage() . "\n";
}

echo "\n=== 參數驗證範例 ===\n\n";

try {
    // 測試無效的參數型別
    echo "測試無效的參數型別...\n";
    file_service_config(123); // 這會拋出例外
} catch (ConfigurationException $e) {
    echo "捕獲到預期的例外: " . $e->getMessage() . "\n\n";
}

try {
    // 測試無效的配置鍵格式
    echo "測試無效的配置鍵格式...\n";
    file_service_config_set('invalid..key', 'value'); // 這會拋出例外
} catch (ConfigurationException $e) {
    echo "捕獲到預期的例外: " . $e->getMessage() . "\n\n";
}

try {
    // 測試缺少參數
    echo "測試缺少參數...\n";
    file_service_config_set('test.key'); // 這會拋出例外
} catch (ConfigurationException $e) {
    echo "捕獲到預期的例外: " . $e->getMessage() . "\n\n";
}

echo "=== 範例完成 ===\n";