<?php

declare(strict_types=1);

/**
 * 配置發布功能使用範例
 * 
 * 展示如何在不同環境中發布和使用配置檔案
 */

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\ConfigPublisher;
use FDMC\FileServiceClient\ConfigLogger;
use FDMC\FileServiceClient\ConfigurationException;
use FDMC\FileServiceClient\GlobalConfigManager;

echo "=== File Service Client 配置發布範例 ===\n\n";

try {
    // 建立配置發布器
    $logger = ConfigLogger::getInstance();
    $publisher = new ConfigPublisher($logger);
    
    echo "1. 檢查當前發布狀態\n";
    echo "------------------------\n";
    
    $status = $publisher->getPublishStatus();
    echo "環境類型：{$status['environment']}\n";
    echo "目標路徑：{$status['target_path']}\n";
    echo "已發布：" . ($status['published'] ? '是' : '否') . "\n";
    
    if ($status['published']) {
        echo "檔案大小：" . formatFileSize($status['file_size']) . "\n";
        echo "修改時間：" . date('Y-m-d H:i:s', $status['modified_time']) . "\n";
        echo "可讀：" . ($status['readable'] ? '是' : '否') . "\n";
        echo "可寫：" . ($status['writable'] ? '是' : '否') . "\n";
    }
    
    echo "\n2. 發布配置檔案\n";
    echo "----------------\n";
    
    // 檢測環境並發布配置
    if (function_exists('config_path')) {
        echo "偵測到 Laravel 環境，使用 Laravel 發布方式\n";
        $success = $publisher->publishToLaravel(null, false);
    } else {
        echo "偵測到原生 PHP 環境，使用原生發布方式\n";
        $success = $publisher->publishToNative(null, false);
    }
    
    if ($success) {
        echo "✓ 配置檔案發布成功！\n";
    } else {
        echo "- 配置檔案發布被取消或已存在\n";
    }
    
    echo "\n3. 測試配置載入\n";
    echo "----------------\n";
    
    // 測試配置載入
    $baseUrl = GlobalConfigManager::config('file-service.baseUrl');
    $timeout = GlobalConfigManager::config('file-service.http.timeout', 30);
    
    echo "基礎 URL：{$baseUrl}\n";
    echo "HTTP 超時：{$timeout} 秒\n";
    
    echo "\n4. 動態配置修改\n";
    echo "----------------\n";
    
    // 動態修改配置
    GlobalConfigManager::set('file-service.baseUrl', 'https://api.example.com');
    GlobalConfigManager::set('file-service.http.timeout', 60);
    
    $newBaseUrl = GlobalConfigManager::config('file-service.baseUrl');
    $newTimeout = GlobalConfigManager::config('file-service.http.timeout');
    
    echo "修改後的基礎 URL：{$newBaseUrl}\n";
    echo "修改後的 HTTP 超時：{$newTimeout} 秒\n";
    
    echo "\n5. 配置檔案自訂發布\n";
    echo "--------------------\n";
    
    // 發布到自訂路徑
    $customPath = __DIR__ . '/custom-config/file-service.php';
    echo "發布到自訂路徑：{$customPath}\n";
    
    try {
        $success = $publisher->publishConfigFile($customPath, true, '自訂路徑');
        if ($success) {
            echo "✓ 自訂路徑發布成功！\n";
            
            // 清理測試檔案
            if (file_exists($customPath)) {
                unlink($customPath);
                $customDir = dirname($customPath);
                if (is_dir($customDir) && count(scandir($customDir)) === 2) {
                    rmdir($customDir);
                }
                echo "✓ 測試檔案已清理\n";
            }
        }
    } catch (ConfigurationException $e) {
        echo "✗ 自訂路徑發布失敗：" . $e->getMessage() . "\n";
    }
    
    echo "\n6. 環境變數覆蓋測試\n";
    echo "--------------------\n";
    
    // 設定環境變數
    putenv('FILE_SERVICE_BASE_URL=https://env.example.com');
    putenv('FILE_SERVICE_TIMEOUT=120');
    
    // 重新載入配置以應用環境變數
    $envBaseUrl = GlobalConfigManager::config('file-service.baseUrl');
    $envTimeout = GlobalConfigManager::config('file-service.http.timeout');
    
    echo "環境變數覆蓋後的基礎 URL：{$envBaseUrl}\n";
    echo "環境變數覆蓋後的 HTTP 超時：{$envTimeout} 秒\n";
    
    // 清理環境變數
    putenv('FILE_SERVICE_BASE_URL');
    putenv('FILE_SERVICE_TIMEOUT');
    
    echo "\n=== 範例執行完成 ===\n";
    
} catch (ConfigurationException $e) {
    echo "✗ 配置錯誤：" . $e->getMessage() . "\n";
    echo "\n修復建議：\n";
    foreach ($e->getRepairSuggestions() as $suggestion) {
        echo "• " . $suggestion . "\n";
    }
} catch (\Exception $e) {
    echo "✗ 未預期的錯誤：" . $e->getMessage() . "\n";
    echo "檔案：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

/**
 * 格式化檔案大小
 * 
 * @param int $bytes 位元組數
 * @return string 格式化後的大小
 */
function formatFileSize(int $bytes): string
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $factor = floor((strlen((string)$bytes) - 1) / 3);
    
    return sprintf("%.1f %s", $bytes / pow(1024, $factor), $units[$factor]);
}