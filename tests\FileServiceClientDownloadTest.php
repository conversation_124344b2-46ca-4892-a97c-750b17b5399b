<?php

namespace Tests;

use PHPUnit\Framework\TestCase;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Stream;
use Psr\Http\Message\StreamInterface;
use FDMC\FileServiceClient\Client\FileServiceClient;
use FDMC\FileServiceClient\Http\HttpClientInterface;
use FDMC\FileServiceClient\Exceptions\FileServiceException;

class FileServiceClientDownloadTest extends TestCase
{
    private $mockHttpClient;
    private $fileServiceClient;

    protected function setUp(): void
    {
        // 模擬 env 函數
        if (!function_exists('env')) {
            eval ('
                function env($key, $default = null) {
                    return $_ENV[$key] ?? $default;
                }
            ');
        }

        $this->mockHttpClient = $this->createMock(HttpClientInterface::class);
        $this->fileServiceClient = new FileServiceClient(['baseUrl' => 'http://test-api.com'], $this->mockHttpClient);
    }

    public function testGetDownloadInfoSuccess()
    {
        // 模擬成功的 download-info 回應
        $expectedResponse = [
            'success' => true,
            'message' => '成功獲取下載資訊',
            'data' => [
                'object_id' => 'file123',
                'name' => 'test.txt',
                'size' => 1024,
                'mime_type' => 'text/plain',
                'storage_type' => 'FS_LOCAL',
                'storage_path' => 'uploads/test.txt',
                'full_path' => '/var/www/files/uploads/test.txt',
                'storage_host' => 'localhost'
            ]
        ];

        $this->mockHttpClient
            ->expects($this->once())
            ->method('sendJsonRequest')
            ->with('GET', '/api/objects/file123/download-info', ['query' => ['user_id' => 1]])
            ->willReturn($expectedResponse);

        $result = $this->fileServiceClient->getDownloadInfo('file123', 1);

        $this->assertEquals($expectedResponse, $result);
        $this->assertEquals('file123', $result['data']['object_id']);
        $this->assertEquals('FS_LOCAL', $result['data']['storage_type']);
    }

    public function testGetDownloadInfoMissingData()
    {
        // 模擬缺少 data 欄位的回應
        $invalidResponse = [
            'success' => true,
            'message' => '成功'
        ];

        $this->mockHttpClient
            ->expects($this->once())
            ->method('sendJsonRequest')
            ->willReturn($invalidResponse);

        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('下載資訊格式錯誤：缺少 data 欄位');

        $this->fileServiceClient->getDownloadInfo('file123', 1);
    }

    public function testDownloadObjectApLocal()
    {
        // 創建臨時測試檔案
        $tempFile = tempnam(sys_get_temp_dir(), 'test_download_');
        file_put_contents($tempFile, 'Test file content');

        try {
            // 模擬 AP_LOCAL 的 download-info 回應
            $downloadInfoResponse = [
                'success' => true,
                'data' => [
                    'object_id' => 'file123',
                    'name' => 'test.txt',
                    'size' => 17, // "Test file content" 的長度
                    'mime_type' => 'text/plain',
                    'storage_type' => 'AP_LOCAL',
                    'storage_path' => 'uploads/test.txt',
                    'full_path' => $tempFile,
                    'storage_host' => ''
                ]
            ];

            $this->mockHttpClient
                ->expects($this->once())
                ->method('sendJsonRequest')
                ->with('GET', '/api/objects/file123/download-info', ['query' => ['user_id' => 1]])
                ->willReturn($downloadInfoResponse);

            $stream = $this->fileServiceClient->downloadObject('file123', 1);

            $this->assertInstanceOf(StreamInterface::class, $stream);
            $this->assertEquals('Test file content', $stream->getContents());

        } finally {
            // 清理臨時檔案
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testDownloadObjectRemote()
    {
        // 模擬 FS_LOCAL 的 download-info 回應
        $downloadInfoResponse = [
            'success' => true,
            'data' => [
                'object_id' => 'file456',
                'name' => 'document.pdf',
                'size' => 2048,
                'mime_type' => 'application/pdf',
                'storage_type' => 'FS_LOCAL',
                'storage_path' => 'documents/document.pdf',
                'full_path' => '/var/www/files/documents/document.pdf',
                'storage_host' => 'localhost'
            ]
        ];

        // 模擬檔案內容
        $fileContent = 'PDF file content';
        $mockResponse = new Response(200, ['Content-Type' => 'application/pdf'], $fileContent);

        $this->mockHttpClient
            ->expects($this->once())
            ->method('sendJsonRequest')
            ->with('GET', '/api/objects/file456/download-info', ['query' => ['user_id' => 1]])
            ->willReturn($downloadInfoResponse);

        $this->mockHttpClient
            ->expects($this->once())
            ->method('sendRawRequest')
            ->with('GET', '/api/objects/file456/download', [
                'query' => ['user_id' => 1],
                'stream' => true,
                'timeout' => 300
            ])
            ->willReturn($mockResponse);

        $stream = $this->fileServiceClient->downloadObject('file456', 1);

        $this->assertInstanceOf(StreamInterface::class, $stream);
        $this->assertEquals($fileContent, $stream->getContents());
    }

    public function testDownloadObjectApLocalFileNotExists()
    {
        // 模擬 AP_LOCAL 但檔案不存在的情況
        $downloadInfoResponse = [
            'success' => true,
            'data' => [
                'object_id' => 'file123',
                'name' => 'test.txt',
                'storage_type' => 'AP_LOCAL',
                'full_path' => '/nonexistent/path/test.txt'
            ]
        ];

        $this->mockHttpClient
            ->expects($this->once())
            ->method('sendJsonRequest')
            ->willReturn($downloadInfoResponse);

        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('本地檔案不存在: /nonexistent/path/test.txt');

        $this->fileServiceClient->downloadObject('file123', 1);
    }

    public function testDownloadObjectInvalidObjectId()
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('物件 ID 不能為空');

        $this->fileServiceClient->downloadObject('', 1);
    }

    public function testDownloadObjectInvalidUserId()
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('使用者 ID 必須是正整數');

        $this->fileServiceClient->downloadObject('file123', 0);
    }

    public function testDownloadObjectPathTraversalAttack()
    {
        // 模擬路徑遍歷攻擊
        $downloadInfoResponse = [
            'success' => true,
            'data' => [
                'object_id' => 'file123',
                'name' => 'test.txt',
                'storage_type' => 'AP_LOCAL',
                'full_path' => '../../../etc/passwd'
            ]
        ];

        $this->mockHttpClient
            ->expects($this->once())
            ->method('sendJsonRequest')
            ->willReturn($downloadInfoResponse);

        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('檔案路徑包含不安全字符: ../../../etc/passwd');

        $this->fileServiceClient->downloadObject('file123', 1);
    }
}
