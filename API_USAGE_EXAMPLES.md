# 物件移動和複製 API 使用說明

本文檔說明如何使用更新後的移動和複製 API，這些 API 現在支援處理重複物件的策略。

## 重複處理策略

當目標位置已存在同名物件時，可以使用以下策略：

- `ignore`: 取消操作，返回錯誤，不執行移動/複製
- `overwrite`: 覆蓋現有物件，刪除現有物件後執行移動/複製

## API 端點

### 移動物件
```http
PUT /objects/{object_id}/move?user_id={user_id}
Content-Type: application/json

{
  "targetId": "目標父級ID",
  "duplicateStrategy": "ignore" // 或 "overwrite"
}
```

### 複製物件
```http
POST /objects/{object_id}/copy?user_id={user_id}
Content-Type: application/json

{
  "targetId": "目標父級ID",
  "newName": "新物件名稱",
  "duplicateStrategy": "ignore" // 或 "overwrite"
}
```

## 使用範例

### 1. 移動物件（發現重複時取消操作）
```bash
curl -X PUT "http://localhost:8080/objects/123e4567-e89b-12d3-a456-426614174000/move?user_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "targetId": "789e4567-e89b-12d3-a456-426614174000",
    "duplicateStrategy": "ignore"
  }'
```

### 2. 移動物件（覆蓋重複）
```bash
curl -X PUT "http://localhost:8080/objects/123e4567-e89b-12d3-a456-426614174000/move?user_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "targetId": "789e4567-e89b-12d3-a456-426614174000",
    "duplicateStrategy": "overwrite"
  }'
```

### 3. 複製物件（發現重複時取消操作）
```bash
curl -X POST "http://localhost:8080/objects/123e4567-e89b-12d3-a456-426614174000/copy?user_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "targetId": "789e4567-e89b-12d3-a456-426614174000",
    "newName": "複製的檔案.txt",
    "duplicateStrategy": "ignore"
  }'
```

### 4. 複製物件（覆蓋重複）
```bash
curl -X POST "http://localhost:8080/objects/123e4567-e89b-12d3-a456-426614174000/copy?user_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "targetId": "789e4567-e89b-12d3-a456-426614174000",
    "newName": "複製的檔案.txt",
    "duplicateStrategy": "overwrite"
  }'
```

## 預設行為

如果未提供 `duplicateStrategy` 參數，系統預設使用 `ignore` 策略（取消操作）。

## 重複物件的定義

重複物件的判斷基於以下條件：
1. 相同的父級物件（同一位置）
2. 相同的物件名稱
3. 相同的物件類型（檔案或資料夾）

## 回應範例

### 成功回應
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "檔案名稱.txt",
    "type": "file",
    "parent": "789e4567-e89b-12d3-a456-426614174000",
    // 其他物件資訊...
  }
}
```

### 錯誤回應
```json
{
  "success": false,
  "message": "錯誤訊息",
  "data": null
}
```

### 重複物件錯誤回應（使用 ignore 策略）
```json
{
  "success": false,
  "message": "移動操作被取消: 目標位置已存在同名物件 '檔案名稱.txt'",
  "data": null
}
```

或

```json
{
  "success": false,
  "message": "複製操作被取消: 目標位置已存在同名物件 '檔案名稱.txt'",
  "data": null
}
``` 