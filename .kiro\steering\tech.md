# 技術堆疊

## 核心技術

- **PHP**: >= 8.0 (現代 PHP 功能、型別宣告、建構子屬性提升)
- **Guzzle HTTP**: ^7.5 (用於 API 通訊的 HTTP 客戶端函式庫)
- **PSR 標準**: PSR-4 自動載入、PSR-7 HTTP 訊息介面

## 開發工具

- **Composer**: 依賴管理和自動載入
- **PHPUnit**: ^9.0 用於單元測試
- **命名空間**: `FDMC\FileServiceClient`

## 建置與測試指令

```bash
# 安裝依賴
composer install

# 執行測試
./vendor/bin/phpunit

# 執行測試並產生覆蓋率報告
./vendor/bin/phpunit --coverage-html coverage

# 更新依賴
composer update

# 驗證 composer.json
composer validate

# 檢查自動載入
composer dump-autoload
```

## 程式碼標準

- **PSR-4**: 自動載入標準
- **型別宣告**: 盡可能使用嚴格型別
- **錯誤處理**: 所有函式庫錯誤使用自訂的 `FileServiceException`
- **依賴注入**: 建構子注入以提高可測試性
- **介面隔離**: 不同關注點使用分離的介面

## HTTP 客戶端架構

- **基於介面**: 使用 `HttpClientInterface` 進行抽象化
- **Guzzle 實現**: `GuzzleHttpClient` 作為具體實現
- **請求類型**: JSON、Multipart（檔案上傳）、Raw（下載）
- **錯誤處理**: 將 Guzzle 例外轉換為 `FileServiceException`

## 配置

- **環境變數**: `FILE_SERVICE_BASE_URL` 用於基礎 URL
- **建構子選項**: 超時、標頭、SSL 驗證
- **彈性初始化**: 支援自訂 HTTP 客戶端注入