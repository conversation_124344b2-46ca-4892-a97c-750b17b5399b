<?php

namespace FDMC\FileServiceClient\Core;

use FDMC\FileServiceClient\Exceptions\FileServiceException;
use FDMC\FileServiceClient\Processing\EnvironmentProcessor;

/**
 * 全域配置管理器
 * 提供類似 Laravel 的全域配置系統，支援即時載入和環境偵測
 */
class GlobalConfigManager
{
    /**
     * 運行時配置存儲（僅保留動態修改的配置）
     */
    private static array $runtimeConfig = [];

    /**
     * Laravel 環境偵測結果快取
     */
    private static ?bool $isLaravel = null;

    /**
     * 配置載入器實例
     */
    private static ?ConfigLoader $configLoader = null;

    /**
     * 環境變數處理器實例
     */
    private static ?EnvironmentProcessor $environmentProcessor = null;

    /**
     * 配置合併器實例
     */
    private static ?ConfigMerger $configMerger = null;

    /**
     * 獲取配置值（主要介面）
     * 
     * @param string|null $key 配置鍵，支援點號語法，null 時返回所有配置
     * @param mixed $default 預設值
     * @return mixed 配置值
     */
    public static function config(?string $key = null, $default = null)
    {
        // 即時載入配置，確保配置是最新的
        $config = self::loadConfig();

        // 合併運行時配置（動態修改的配置優先）
        $config = self::mergeRuntimeConfig($config);

        // 如果沒有指定鍵，返回所有配置
        if ($key === null) {
            return $config;
        }

        // 使用點號語法獲取巢狀配置值
        return self::getNestedValue($config, $key, $default);
    }

    /**
     * 動態設定配置值（僅在運行時有效）
     * 
     * @param string|array $key 配置鍵或配置陣列
     * @param mixed $value 配置值（當 $key 為字串時使用）
     * @return void
     */
    public static function set($key, $value = null): void
    {
        if (is_array($key)) {
            // 批量設定配置
            foreach ($key as $configKey => $configValue) {
                self::setNestedValue(self::$runtimeConfig, $configKey, $configValue);
            }
        } else {
            // 單一配置設定
            self::setNestedValue(self::$runtimeConfig, $key, $value);
        }
    }

    /**
     * 移除運行時配置項目
     * 
     * @param string $key 配置鍵
     * @return void
     */
    public static function forget(string $key): void
    {
        self::unsetNestedValue(self::$runtimeConfig, $key);
    }

    /**
     * 獲取所有配置
     * 
     * @return array 所有配置
     */
    public static function all(): array
    {
        return self::config();
    }

    /**
     * 偵測是否為 Laravel 環境
     * 
     * @return bool true 為 Laravel 環境，false 為原生 PHP 環境
     */
    public static function isLaravel(): bool
    {
        if (self::$isLaravel === null) {
            self::$isLaravel = self::detectLaravelEnvironment();
        }

        return self::$isLaravel;
    }

    /**
     * 載入配置（即時載入策略）
     * 每次都重新載入，確保配置是最新的
     * 
     * @return array 載入的配置
     */
    public static function loadConfig(): array
    {
        if (self::isLaravel()) {
            return self::loadLaravelConfig();
        } else {
            return self::loadNativeConfig();
        }
    }

    /**
     * 清除運行時配置
     * 
     * @return void
     */
    public static function clearRuntimeConfig(): void
    {
        self::$runtimeConfig = [];
    }

    /**
     * 重置環境偵測快取（主要用於測試）
     * 
     * @return void
     */
    public static function resetEnvironmentDetection(): void
    {
        self::$isLaravel = null;
    }

    /**
     * 偵測 Laravel 環境
     * 
     * @return bool
     */
    private static function detectLaravelEnvironment(): bool
    {
        // 檢查 Laravel 核心函數是否存在
        if (!function_exists('config') || !function_exists('app')) {
            return false;
        }

        // 檢查是否存在 Laravel 應用實例
        try {
            if (function_exists('app') && app() instanceof \Illuminate\Foundation\Application) {
                return true;
            }
        } catch (\Exception $e) {
            // 如果無法獲取應用實例，繼續其他檢查
        }

        // 檢查是否存在 Laravel 特有的常數或類別
        if (defined('LARAVEL_START') || class_exists('\Illuminate\Foundation\Application')) {
            return true;
        }

        // 檢查 composer.json 中是否有 Laravel 依賴
        $composerPath = self::findComposerJson();
        if ($composerPath && self::hasLaravelDependency($composerPath)) {
            return true;
        }

        return false;
    }

    /**
     * 載入 Laravel 配置
     * 
     * @return array
     */
    private static function loadLaravelConfig(): array
    {
        try {
            // 使用 Laravel 的 config 函數獲取配置
            $config = config('file-service', []);

            // 如果配置為空，嘗試載入預設配置
            if (empty($config)) {
                $config = self::getDefaultConfig();
            }

            return $config;
        } catch (\Exception $e) {
            // 如果 Laravel 配置載入失敗，回退到預設配置
            return self::getDefaultConfig();
        }
    }

    /**
     * 載入原生 PHP 配置
     * 使用改進的配置合併邏輯
     * 
     * @return array
     */
    private static function loadNativeConfig(): array
    {
        $loader = self::getConfigLoader();
        $processor = self::getEnvironmentProcessor();
        $merger = self::getConfigMerger();

        // 1. 獲取預設配置
        $defaultConfig = self::getDefaultConfig();

        // 2. 載入專案配置檔案
        $projectConfig = [];
        try {
            $projectConfig = $loader->loadFromFile();
        } catch (FileServiceException $e) {
            // 配置檔案載入失敗時，記錄警告但繼續使用預設配置
            error_log("配置檔案載入失敗，使用預設配置: " . $e->getMessage());
        }

        // 3. 處理環境變數覆蓋
        $envOverrides = [];
        try {
            // 從環境變數中提取覆蓋配置
            $envOverrides = $processor->extractEnvOverrides();
        } catch (\Exception $e) {
            error_log("環境變數處理失敗: " . $e->getMessage());
        }

        // 4. 使用配置合併器合併所有配置
        return $merger->mergeAllConfigs(
            $defaultConfig,
            $projectConfig,
            $envOverrides
        );
    }

    /**
     * 合併運行時配置
     * 使用配置合併器處理運行時配置
     * 
     * @param array $config 基礎配置
     * @return array 合併後的配置
     */
    private static function mergeRuntimeConfig(array $config): array
    {
        if (empty(self::$runtimeConfig)) {
            return $config;
        }

        $merger = self::getConfigMerger();
        return $merger->mergeRecursive($config, self::$runtimeConfig);
    }

    /**
     * 獲取配置載入器實例
     * 
     * @return ConfigLoader
     */
    private static function getConfigLoader(): ConfigLoader
    {
        if (self::$configLoader === null) {
            self::$configLoader = new ConfigLoader();
        }

        return self::$configLoader;
    }

    /**
     * 獲取環境變數處理器實例
     * 
     * @return EnvironmentProcessor
     */
    private static function getEnvironmentProcessor(): EnvironmentProcessor
    {
        if (self::$environmentProcessor === null) {
            self::$environmentProcessor = new EnvironmentProcessor();
        }

        return self::$environmentProcessor;
    }

    /**
     * 獲取配置合併器實例
     * 
     * @return ConfigMerger
     */
    private static function getConfigMerger(): ConfigMerger
    {
        if (self::$configMerger === null) {
            self::$configMerger = new ConfigMerger();
        }

        return self::$configMerger;
    }
    /**
     * 獲取巢狀陣列的值
     * 
     * @param array $array 陣列
     * @param string $key 鍵，支援點號分隔
     * @param mixed $default 預設值
     * @return mixed 值
     */
    private static function getNestedValue(array $array, string $key, $default = null)
    {
        if (strpos($key, '.') === false) {
            return $array[$key] ?? $default;
        }

        $keys = explode('.', $key);
        $value = $array;

        foreach ($keys as $nestedKey) {
            if (!is_array($value) || !array_key_exists($nestedKey, $value)) {
                return $default;
            }
            $value = $value[$nestedKey];
        }

        return $value;
    }

    /**
     * 設定巢狀陣列的值
     * 
     * @param array $array 陣列（引用傳遞）
     * @param string $key 鍵，支援點號分隔
     * @param mixed $value 值
     * @return void
     */
    private static function setNestedValue(array &$array, string $key, $value): void
    {
        if (strpos($key, '.') === false) {
            $array[$key] = $value;
            return;
        }

        $keys = explode('.', $key);
        $current = &$array;

        foreach ($keys as $nestedKey) {
            if (!isset($current[$nestedKey]) || !is_array($current[$nestedKey])) {
                $current[$nestedKey] = [];
            }
            $current = &$current[$nestedKey];
        }

        $current = $value;
    }

    /**
     * 移除巢狀陣列的值
     * 
     * @param array $array 陣列（引用傳遞）
     * @param string $key 鍵，支援點號分隔
     * @return void
     */
    private static function unsetNestedValue(array &$array, string $key): void
    {
        if (strpos($key, '.') === false) {
            unset($array[$key]);
            return;
        }

        $keys = explode('.', $key);
        $current = &$array;
        $lastKey = array_pop($keys);

        foreach ($keys as $nestedKey) {
            if (!isset($current[$nestedKey]) || !is_array($current[$nestedKey])) {
                return; // 路徑不存在，無需移除
            }
            $current = &$current[$nestedKey];
        }

        unset($current[$lastKey]);
    }

    /**
     * 遞迴合併陣列
     * 
     * @param array $array1 第一個陣列
     * @param array $array2 第二個陣列
     * @return array 合併後的陣列
     */
    private static function arrayMergeRecursive(array $array1, array $array2): array
    {
        $merged = $array1;

        foreach ($array2 as $key => $value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = self::arrayMergeRecursive($merged[$key], $value);
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }

    /**
     * 尋找 composer.json 檔案
     * 
     * @return string|null composer.json 檔案路徑
     */
    private static function findComposerJson(): ?string
    {
        $currentDir = getcwd();
        $maxDepth = 10; // 最多向上搜尋 10 層目錄

        for ($i = 0; $i < $maxDepth; $i++) {
            $composerPath = $currentDir . DIRECTORY_SEPARATOR . 'composer.json';

            if (file_exists($composerPath)) {
                return $composerPath;
            }

            $parentDir = dirname($currentDir);
            if ($parentDir === $currentDir) {
                break; // 已到達根目錄
            }

            $currentDir = $parentDir;
        }

        return null;
    }

    /**
     * 檢查 composer.json 中是否有 Laravel 依賴
     * 
     * @param string $composerPath composer.json 檔案路徑
     * @return bool
     */
    private static function hasLaravelDependency(string $composerPath): bool
    {
        try {
            $composerContent = file_get_contents($composerPath);
            $composerData = json_decode($composerContent, true);

            if (!is_array($composerData)) {
                return false;
            }

            // 檢查 require 和 require-dev 中是否有 Laravel 相關套件
            $dependencies = array_merge(
                $composerData['require'] ?? [],
                $composerData['require-dev'] ?? []
            );

            $laravelPackages = [
                'laravel/framework',
                'illuminate/support',
                'illuminate/container',
                'illuminate/config'
            ];

            foreach ($laravelPackages as $package) {
                if (isset($dependencies[$package])) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 獲取預設配置
     * 
     * @return array
     */
    private static function getDefaultConfig(): array
    {
        return [
            'baseUrl' => $_ENV['FILE_SERVICE_BASE_URL'] ?? 'http://localhost:8080',
            'http' => [
                'timeout' => (int) ($_ENV['FILE_SERVICE_TIMEOUT'] ?? 30),
                'connect_timeout' => (int) ($_ENV['FILE_SERVICE_CONNECT_TIMEOUT'] ?? 10),
                'read_timeout' => (int) ($_ENV['FILE_SERVICE_READ_TIMEOUT'] ?? 60),
                'verify' => filter_var($_ENV['FILE_SERVICE_SSL_VERIFY'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'http_errors' => false,
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'FDMC-FileServiceClient/1.0',
            ],
            'upload' => [
                'timeout' => (int) ($_ENV['FILE_SERVICE_UPLOAD_TIMEOUT'] ?? 120),
                'max_file_size' => (int) ($_ENV['FILE_SERVICE_MAX_FILE_SIZE'] ?? 104857600), // 100MB
                'allowed_mime_types' => [
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                    'image/webp',
                    'application/pdf',
                    'text/plain',
                    'application/json',
                    'application/zip',
                    'application/x-zip-compressed',
                ],
            ],
            'download' => [
                'timeout' => (int) ($_ENV['FILE_SERVICE_DOWNLOAD_TIMEOUT'] ?? 300),
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800, // 50MB
            ],
            'cache' => [
                'enabled' => filter_var($_ENV['FILE_SERVICE_CACHE_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'driver' => $_ENV['FILE_SERVICE_CACHE_DRIVER'] ?? 'file',
                'ttl' => (int) ($_ENV['FILE_SERVICE_CACHE_TTL'] ?? 3600),
            ],
            'logging' => [
                'enabled' => filter_var($_ENV['FILE_SERVICE_LOG_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
                'level' => $_ENV['FILE_SERVICE_LOG_LEVEL'] ?? 'info',
                'channel' => $_ENV['FILE_SERVICE_LOG_CHANNEL'] ?? 'default',
            ],
            'retry' => [
                'max_attempts' => (int) ($_ENV['FILE_SERVICE_RETRY_MAX_ATTEMPTS'] ?? 3),
                'delay' => (int) ($_ENV['FILE_SERVICE_RETRY_DELAY'] ?? 1000),
                'multiplier' => (int) ($_ENV['FILE_SERVICE_RETRY_MULTIPLIER'] ?? 2),
            ],
        ];
    }
}