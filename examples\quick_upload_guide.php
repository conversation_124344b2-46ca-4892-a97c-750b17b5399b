<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Fdmc\FileServiceClient\FileServiceClient;
use Fdmc\FileServiceClient\FileServiceException;

/**
 * 📤 檔案上傳快速指南
 * 
 * 這是一個簡潔的上傳功能使用指南，展示基本的兩步驟上傳流程。
 */

echo "📤 檔案上傳快速指南\n";
echo "====================\n\n";

// 初始化客戶端
$client = new FileServiceClient([
    'base_uri' => 'https://api.fileservice.com',
    'timeout' => 60
]);

$client->setHeaders([
    'Authorization' => 'Bearer your-access-token'
]);

// 設定參數
$companyId = 1;
$scope = 'uploads';
$userId = 123;

try {

    echo "📋 兩步驟上傳流程：\n";
    echo "1. 先創建檔案物件\n";
    echo "2. 再上傳檔案內容\n\n";

    // ===========================================
    // 步驟 1: 創建檔案物件
    // ===========================================
    echo "🔧 步驟 1: 創建檔案物件\n";
    echo "------------------------\n";

    $objectData = $client->buildObjectData(
        companyId: $companyId,
        scope: $scope,
        name: 'my-document.pdf',
        type: 'file',
        mimeType: 'application/pdf'
    );

    $object = $client->createObject($userId, $objectData);
    $objectId = $object['data']['id'];

    echo "✅ 檔案物件已創建\n";
    echo "   物件 ID: {$objectId}\n";
    echo "   檔案名稱: {$object['data']['name']}\n\n";

    // ===========================================
    // 步驟 2: 上傳檔案內容
    // ===========================================
    echo "📤 步驟 2: 上傳檔案內容\n";
    echo "------------------------\n";

    // 創建測試檔案（實際使用時請替換為真實檔案路徑）
    $testFilePath = sys_get_temp_dir() . '/test-document.pdf';
    file_put_contents($testFilePath, "模擬的 PDF 檔案內容...\n這裡是測試資料");

    // 上傳檔案內容到已創建的物件
    $uploadResult = $client->uploadFile($userId, $testFilePath, $objectId);

    echo "✅ 檔案內容上傳完成\n";
    echo "   上傳狀態: " . ($uploadResult['success'] ? '成功' : '失敗') . "\n";
    echo "   API 回應: " . json_encode($uploadResult, JSON_UNESCAPED_UNICODE) . "\n\n";

    // 清理測試檔案
    unlink($testFilePath);

    // ===========================================
    // 驗證上傳結果
    // ===========================================
    echo "🔍 驗證上傳結果\n";
    echo "----------------\n";

    $objectInfo = $client->getObjectInfo($objectId, $userId);
    echo "✅ 檔案資訊:\n";
    echo "   名稱: {$objectInfo['data']['name']}\n";
    echo "   大小: {$objectInfo['data']['size']} bytes\n";
    echo "   類型: {$objectInfo['data']['type']}\n";
    echo "   MIME: {$objectInfo['data']['mimeType']}\n\n";

    echo "🎉 上傳流程完成！\n\n";

    // ===========================================
    // 其他上傳方式範例
    // ===========================================
    echo "💡 其他上傳方式：\n";
    echo "==================\n\n";

    // 從內容上傳
    echo "📝 從內容上傳:\n";
    $contentObjectData = $client->buildObjectData(
        companyId: $companyId,
        scope: $scope,
        name: 'content-file.txt',
        type: 'file',
        mimeType: 'text/plain'
    );

    $contentObject = $client->createObject($userId, $contentObjectData);
    $contentObjectId = $contentObject['data']['id'];

    $fileContent = "這是直接從字串內容上傳的檔案";
    $uploadResult = $client->uploadFileFromContent($userId, $fileContent, 'content-file.txt', $contentObjectId);
    echo "✅ 內容上傳完成 (物件 ID: {$contentObjectId})\n\n";

    // 從串流上傳
    echo "🌊 從串流上傳:\n";
    $streamObjectData = $client->buildObjectData(
        companyId: $companyId,
        scope: $scope,
        name: 'stream-file.csv',
        type: 'file',
        mimeType: 'text/csv'
    );

    $streamObject = $client->createObject($userId, $streamObjectData);
    $streamObjectId = $streamObject['data']['id'];

    $stream = fopen('php://memory', 'r+');
    fwrite($stream, "欄位1,欄位2,欄位3\n值1,值2,值3\n");
    rewind($stream);

    $uploadResult = $client->uploadFileFromStream($userId, $stream, 'stream-file.csv', $streamObjectId);
    fclose($stream);
    echo "✅ 串流上傳完成 (物件 ID: {$streamObjectId})\n\n";

} catch (FileServiceException $e) {
    echo "❌ 檔案服務錯誤: " . $e->getMessage() . "\n";
    echo "   錯誤代碼: " . $e->getCode() . "\n";
} catch (Exception $e) {
    echo "❌ 系統錯誤: " . $e->getMessage() . "\n";
}

echo "📚 詳細範例請參考: examples/upload_example.php\n";
echo "📖 完整文檔請參考: README.md\n";

/**
 * 🚀 快速參考：
 * 
 * // 基本上傳流程
 * $object = $client->createObject($userId, $objectData);
 * $result = $client->uploadFile($userId, $filePath, $object['data']['id']);
 * 
 * // 可用的上傳方法：
 * - uploadFile($userId, $filePath, $objectId, $progressCallback = null)
 * - uploadFileFromContent($userId, $content, $fileName, $objectId, $progressCallback = null)
 * - uploadFileFromStream($userId, $stream, $fileName, $objectId, $progressCallback = null)
 * 
 * 重要提醒：
 * - 必須先使用 createObject() 創建物件
 * - 然後使用上傳方法上傳內容到該物件
 * - 支援進度回調監控上傳狀態
 * - 記得處理異常和清理資源
 */