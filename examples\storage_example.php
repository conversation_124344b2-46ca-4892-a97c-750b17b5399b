<?php

require_once 'vendor/autoload.php';

use Fdmc\FileServiceClient\StorageManager;
use Fdmc\FileServiceClient\FileServiceException;

// 初始化儲存管理器
$storageManager = new StorageManager('http://localhost:8000'); // 替換為實際的 file-service URL

// 基本參數
$userId = 123;

try {
    // ===== 儲存管理示例 =====

    // 1. 列出所有儲存
    echo "=== 列出所有儲存 ===\n";
    $storages = $storageManager->listStorages($userId);
    print_r($storages);

    // 2. 創建本地儲存
    echo "\n=== 創建本地儲存 ===\n";
    $localStorageData = $storageManager->buildStorageData(
        name: '本地檔案儲存',
        type: 'FS_LOCAL',
        host: 'localhost',
        remark: '本地檔案系統儲存',
        payload: ['path' => '/var/files']
    );
    $localStorage = $storageManager->createStorage($userId, $localStorageData);
    print_r($localStorage);

    // 3. 創建 S3 儲存
    echo "\n=== 創建 S3 儲存 ===\n";
    $s3StorageData = $storageManager->buildStorageData(
        name: 'AWS S3 儲存',
        type: 'S3',
        host: 's3.amazonaws.com',
        remark: 'Amazon S3 雲端儲存',
        payload: [
            'bucket' => 'my-bucket',
            'region' => 'us-west-2',
            'access_key' => 'your-access-key',
            'secret_key' => 'your-secret-key'
        ]
    );
    $s3Storage = $storageManager->createStorage($userId, $s3StorageData);
    print_r($s3Storage);

    // 4. 如果有成功創建的儲存，獲取其詳情
    if (isset($localStorage['data']['id'])) {
        echo "\n=== 獲取儲存詳情 ===\n";
        $storageInfo = $storageManager->getStorage($localStorage['data']['id'], $userId);
        print_r($storageInfo);

        // 5. 更新儲存資訊
        echo "\n=== 更新儲存 ===\n";
        $updateData = [
            'remark' => '已更新的本地檔案系統儲存',
            'payload' => ['path' => '/var/updated-files']
        ];
        $updatedStorage = $storageManager->updateStorage($localStorage['data']['id'], $userId, $updateData);
        print_r($updatedStorage);
    }

    // 6. 設置自定義標頭
    echo "\n=== 設置自定義標頭 ===\n";
    $storageManager->setHeaders([
        'Authorization' => 'Bearer your-token',
        'X-Custom-Header' => 'Custom-Value'
    ]);
    echo "已設置自定義標頭\n";

    // 7. 示範各種儲存類型
    echo "\n=== 示範各種儲存類型 ===\n";
    $storageTypes = [
        'FTP' => [
            'name' => 'FTP 儲存',
            'host' => 'ftp.example.com',
            'payload' => [
                'username' => 'ftp-user',
                'password' => 'ftp-password',
                'port' => 21
            ]
        ],
        'NAS' => [
            'name' => 'NAS 儲存',
            'host' => '*************',
            'payload' => [
                'share_path' => '/shared/files',
                'protocol' => 'SMB'
            ]
        ],
        'AP_LOCAL' => [
            'name' => '應用程式本地儲存',
            'host' => 'localhost',
            'payload' => [
                'app_path' => '/app/storage'
            ]
        ]
    ];

    foreach ($storageTypes as $type => $config) {
        $storageData = $storageManager->buildStorageData(
            name: $config['name'],
            type: $type,
            host: $config['host'],
            remark: "示範 {$type} 儲存類型",
            payload: $config['payload']
        );

        echo "建立 {$type} 儲存資料:\n";
        print_r($storageData);
        echo "\n";
    }

} catch (FileServiceException $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
    echo "錯誤代碼: " . $e->getCode() . "\n";
} catch (Exception $e) {
    echo "未預期的錯誤: " . $e->getMessage() . "\n";
}