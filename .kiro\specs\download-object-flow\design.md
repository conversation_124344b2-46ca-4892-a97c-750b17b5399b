# 設計文件

## 概述

本設計文件描述了 downloadObject 完整流程的架構設計，包括 FileServiceClient (FSC) 與 file-service (FS) 之間的協調機制。系統將支援多種儲存類型（FS_LOCAL、AP_LOCAL 及其他外部服務），並透過串流方式進行高效的檔案傳輸。

## 架構

### 整體架構圖

```mermaid
sequenceDiagram
    participant Client as 客戶端應用
    participant FSC as FileServiceClient
    participant FS as File-Service
    participant Storage as 儲存系統

    Client->>FSC: downloadObject(objectId, userId)
    FSC->>FS: getObjectInfo(objectId, userId)
    FS-->>FSC: 物件資訊 + storage.type
    
    alt storage.type == "AP_LOCAL"
        FSC->>FSC: 檢查本地檔案存在
        FSC->>FSC: 建立本地檔案串流
        FSC-->>Client: 回傳檔案串流
    else storage.type != "AP_LOCAL"
        FSC->>FS: downloadRequest(objectId, userId)
        FS->>FS: 驗證物件存在
        FS->>Storage: 根據 storage 資訊取得檔案
        Storage-->>FS: 檔案串流
        FS-->>FSC: 檔案串流
        FSC-->>Client: 回傳檔案串流
    end
```

### 儲存類型架構

```mermaid
graph TD
    A[物件請求] --> B{檢查 Storage Type}
    B -->|AP_LOCAL| C[客戶端本地目錄]
    B -->|FS_LOCAL| D[伺服器本地目錄]
    B -->|其他類型| E[外部儲存服務]
    
    C --> F[直接串流回傳]
    D --> G[透過 FS 串流回傳]
    E --> H[透過 FS 連接外部服務]
    
    G --> I[檔案串流]
    H --> I
    F --> I
```

## 元件與介面

### FileServiceClient 元件

#### 現有方法分析
目前的 `downloadObject` 方法已經實作了基本的下載邏輯：
1. 先透過 `getObjectInfo` 取得物件資訊
2. 檢查是否有 `storage_path` 且檔案存在於本地
3. 如果是本地檔案，直接建立串流回傳
4. 如果不是本地檔案，向 file-service 發送下載請求

#### 需要優化的部分
根據您的需求，現有實作需要調整以支援不同的儲存類型判斷：

**問題分析：**
- 目前只檢查 `storage_path` 是否存在，沒有根據 `storage.type` 來判斷
- 缺乏對 AP_LOCAL、FS_LOCAL 等儲存類型的明確處理
- 需要更清楚的儲存類型判斷邏輯

**建議的調整方向：**
- 修改 `downloadObject` 方法的邏輯，根據回傳的儲存類型資訊來決定處理方式
- 保持現有的串流處理機制
- 加強錯誤處理和驗證機制

### File-Service 元件

#### 需要實作或調整的 API 端點

**物件資訊 API (`/api/objects/{objectId}/metadata`)**
- 需要確保回傳的資料包含儲存類型資訊
- 支援透過業務邏輯判斷物件對應的儲存類型
- 回傳足夠的資訊供 FSC 判斷下載策略

**下載 API (`/api/objects/{objectId}/download`)**
- 處理非 AP_LOCAL 類型的下載請求
- 根據物件的儲存資訊定位實際檔案
- 以串流方式回傳檔案內容
- 支援不同儲存類型的檔案存取

#### 服務層設計

**Object Service**
- 提供物件資訊查詢功能
- 整合儲存類型判斷邏輯
- 建立 DTO 來組合下載所需的完整資訊

**Storage Service** 
- 根據儲存類型解析檔案實際位置
- 處理不同儲存類型的檔案存取邏輯
- 提供統一的檔案串流介面

## 資料模型

### Model 優化思路說明

基於下載流程的需求分析和成本效益考量，我們採用**保持現有結構不變**的策略：

1. **問題識別**：下載流程需要知道檔案的實際儲存位置和類型
2. **解決方案**：透過服務層的二次轉換來組合所需資訊，而不是修改底層 Model
3. **優勢**：避免大規模重構，降低風險，保持系統穩定性

**實作策略：**
- Object Model 保持您現有的結構不變
- 透過業務邏輯來判斷儲存位置和類型
- 使用 DTO 模式來組合下載所需的資訊

### Storage Model 分析與建議

**現狀分析：**
根據您的說明，Storage 應該是記錄伺服器資訊的獨立實體，不應該包含 ObjectID 這類關聯欄位。這表示 Storage 更像是一個「儲存節點」的概念，而不是「物件儲存記錄」。

**成本效益分析：**

1. **直接修改 Object 結構的成本：**
   - 需要修改所有相關的 CRUD 函數
   - 需要更新資料庫 schema
   - 需要修改所有使用 Object 的 API 回應
   - 可能影響現有的業務邏輯
   - **成本：高，風險：高**

2. **使用二次轉換的方案：**
   - 保持現有 Object 和 Storage 結構不變
   - 在下載流程中建立專用的 DTO (Data Transfer Object)
   - 透過服務層進行資料組合和轉換
   - **成本：低，風險：低**

**Storage Model 處理原則：**
- Storage 作為獨立的儲存節點實體，不包含 ObjectID 等關聯欄位
- 保持 Storage Model 的簡潔性，專注於記錄伺服器基本資訊
- 透過業務邏輯來建立 Object 與 Storage 之間的關聯關係

**推薦方案：二次轉換 + DTO**

```go
// 專用於下載流程的 DTO
type ObjectDownloadInfo struct {
    ObjectID     string  `json:"object_id"`
    Name         string  `json:"name"`
    Size         *int64  `json:"size"`
    MimeType     *string `json:"mime_type"`
    StorageType  string  `json:"storage_type"`
    StoragePath  string  `json:"storage_path"`
    FullPath     string  `json:"full_path"` // 組合後的完整路徑
}

// 在服務層進行轉換
func (s *ObjectService) GetDownloadInfo(objectID string) (*ObjectDownloadInfo, error) {
    // 1. 取得 Object 資訊
    // 2. 根據業務規則判斷對應的 Storage
    // 3. 組合成 ObjectDownloadInfo 回傳
}
```

這樣的設計：
- 不影響現有結構
- 專注於下載流程的需求
- 易於測試和維護
- 未來如需調整也更靈活
```

### API 回應結構

```go
type ObjectInfoResponse struct {
    Success bool   `json:"success"`
    Data    Object `json:"data"`
    Message string `json:"message,omitempty"`
}

type DownloadResponse struct {
    // 直接回傳檔案串流，不使用 JSON 包裝
    // Content-Type: application/octet-stream 或實際檔案類型
    // Content-Disposition: attachment; filename="檔案名稱"
}
```

## 錯誤處理

### 錯誤類型定義

#### PHP 端錯誤
- `FileServiceException`: 基礎例外類別
  - `FileNotFoundException`: 檔案不存在
  - `FileAccessException`: 檔案無法存取
  - `StorageTypeException`: 不支援的儲存類型
  - `StreamException`: 串流處理錯誤

#### Go 端錯誤
```go
var (
    ErrObjectNotFound    = errors.New("物件不存在")
    ErrFileNotFound      = errors.New("檔案不存在")
    ErrInvalidStorageType = errors.New("不支援的儲存類型")
    ErrFileAccessDenied  = errors.New("檔案存取被拒絕")
    ErrStreamCreationFailed = errors.New("串流建立失敗")
)
```

### 錯誤處理流程

```mermaid
graph TD
    A[開始下載] --> B{物件是否存在?}
    B -->|否| C[回傳 404 錯誤]
    B -->|是| D{儲存類型是否支援?}
    D -->|否| E[回傳 400 錯誤]
    D -->|是| F{檔案是否存在?}
    F -->|否| G[回傳 404 錯誤]
    F -->|是| H{檔案是否可讀?}
    H -->|否| I[回傳 403 錯誤]
    H -->|是| J[建立串流]
    J --> K{串流建立成功?}
    K -->|否| L[回傳 500 錯誤]
    K -->|是| M[回傳檔案串流]
```

## 測試策略

### 單元測試

#### PHP 端測試
- `FileServiceClientTest::testDownloadObjectWithLocalStorage()`
  - 測試 AP_LOCAL 類型的本地檔案下載
  - 模擬檔案存在和不存在的情況
  - 驗證串流回傳正確性

- `FileServiceClientTest::testDownloadObjectWithRemoteStorage()`
  - 測試非 AP_LOCAL 類型的遠端下載
  - 模擬 HTTP 請求和回應
  - 驗證串流處理正確性

- `FileServiceClientTest::testDownloadObjectErrorHandling()`
  - 測試各種錯誤情況的處理
  - 驗證例外拋出的正確性
  - 測試資源清理

#### Go 端測試
- `ObjectHandlerTest::TestGetObjectMetadata()`
  - 測試物件資訊取得功能
  - 驗證權限檢查
  - 測試不同儲存類型的回應

- `ObjectHandlerTest::TestDownloadObject()`
  - 測試檔案下載功能
  - 驗證串流建立和回傳
  - 測試大檔案處理

- `StorageServiceTest::TestResolveStoragePath()`
  - 測試路徑解析功能
  - 驗證不同儲存類型的處理
  - 測試安全性驗證

### 整合測試

- `DownloadFlowIntegrationTest`
  - 測試完整的下載流程
  - 驗證 FSC 與 FS 之間的協調
  - 測試不同儲存類型的端到端流程

### 效能測試

- 大檔案下載測試（>100MB）
- 並發下載測試
- 記憶體使用量測試
- 串流效能測試

## 安全考量

### 路徑安全
- 防止路徑遍歷攻擊（../ 等）
- 驗證檔案路徑在允許的目錄範圍內
- 檔案名稱安全性檢查

### 權限控制
- 使用者權限驗證
- 物件存取權限檢查
- 檔案系統權限驗證

### 資料完整性
- 檔案雜湊值驗證（SHA1/MD5）
- 串流傳輸完整性檢查
- 錯誤時的資源清理

## 效能優化

### 串流處理
- 使用緩衝串流減少 I/O 操作
- 適當的緩衝區大小設定
- 記憶體使用量控制

### 快取策略
- 物件資訊快取
- 檔案路徑解析快取
- HTTP 回應快取頭設定

### 資源管理
- 檔案控制代碼及時釋放
- 串流資源自動清理
- 連線池管理