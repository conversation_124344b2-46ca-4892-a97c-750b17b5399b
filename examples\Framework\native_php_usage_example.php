<?php

/**
 * 原生 PHP 專案使用範例
 * 
 * 展示如何在原生 PHP 專案中使用 File Service Client
 * 不依賴任何框架，完全獨立使用
 */

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\FileServiceClient;
use FDMC\FileServiceClient\StorageManager;
use FDMC\FileServiceClient\GlobalConfigManager;
use FDMC\FileServiceClient\ConfigManager;
use FDMC\FileServiceClient\ConfigPublisher;

// 載入全域配置函數
require_once __DIR__ . '/../src/helpers.php';

// 模擬 env 函數（在原生 PHP 中）
if (!function_exists('env')) {
    function env($key, $default = null) {
        $value = $_ENV[$key] ?? getenv($key);
        if ($value === false) {
            return $default;
        }
        return $value;
    }
}

echo "=== 原生 PHP 專案使用範例 ===\n\n";

// 1. 項目初始化
echo "1. 項目初始化\n";
echo "在原生 PHP 專案中，您需要手動載入自動載入器：\n";
echo "```php\n";
echo "require_once 'vendor/autoload.php';\n";
echo "```\n\n";

// 2. 發布配置檔案到專案
echo "2. 發布配置檔案到專案\n";

$configDir = __DIR__ . '/../config-example';
if (!is_dir($configDir)) {
    mkdir($configDir, 0755, true);
}

try {
    $publisher = new ConfigPublisher();
    $sourceConfig = __DIR__ . '/../config/file-service.php';
    $targetConfig = $configDir . '/file-service.php';
    
    if (file_exists($sourceConfig)) {
        copy($sourceConfig, $targetConfig);
        echo "✓ 配置檔案已複製到: {$targetConfig}\n";
        echo "您可以根據需要修改此配置檔案\n";
    } else {
        echo "⚠ 未找到源配置檔案，使用內建預設配置\n";
    }
} catch (Exception $e) {
    echo "配置檔案發布失敗: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. 基本使用方式
echo "3. 基本使用方式\n";

try {
    // 方式 1: 直接創建客戶端（使用預設配置）
    echo "方式 1: 使用預設配置創建客戶端\n";
    $client1 = new FileServiceClient();
    echo "- 基礎 URL: " . $client1->getBaseUrl() . "\n";
    
    // 方式 2: 使用自定義配置創建客戶端
    echo "\n方式 2: 使用自定義配置創建客戶端\n";
    $customConfig = [
        'baseUrl' => 'https://api.example.com',
        'http' => [
            'timeout' => 60,
            'connect_timeout' => 15,
        ],
        'headers' => [
            'Authorization' => 'Bearer your-api-token',
            'X-Client-Version' => '1.0.0',
        ]
    ];
    
    $client2 = new FileServiceClient($customConfig);
    echo "- 基礎 URL: " . $client2->getBaseUrl() . "\n";
    
} catch (Exception $e) {
    echo "客戶端創建失敗: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. 使用全域配置管理器
echo "4. 使用全域配置管理器\n";

echo "環境偵測結果: " . (GlobalConfigManager::isLaravel() ? 'Laravel' : '原生 PHP') . "\n";

// 獲取配置
$baseUrl = GlobalConfigManager::config('baseUrl');
$timeout = GlobalConfigManager::config('http.timeout', 30);

echo "配置值:\n";
echo "- Base URL: {$baseUrl}\n";
echo "- Timeout: {$timeout} 秒\n";

// 動態修改配置
GlobalConfigManager::set('custom.api_key', 'your-api-key');
GlobalConfigManager::set([
    'custom.debug' => true,
    'custom.environment' => 'development'
]);

echo "動態設定的配置:\n";
echo "- API Key: " . GlobalConfigManager::config('custom.api_key') . "\n";
echo "- Debug: " . (GlobalConfigManager::config('custom.debug') ? 'true' : 'false') . "\n";
echo "- Environment: " . GlobalConfigManager::config('custom.environment') . "\n";

echo "\n";

// 5. 使用全域函數
echo "5. 使用全域函數 (推薦方式)\n";

echo "使用 file_service_config() 函數:\n";
echo "- Base URL: " . file_service_config('baseUrl') . "\n";
echo "- Upload timeout: " . file_service_config('upload.timeout', 120) . " 秒\n";
echo "- Max file size: " . number_format(file_service_config('upload.max_file_size', 0) / 1024 / 1024, 2) . " MB\n";

// 批量設定配置
file_service_config([
    'app.name' => 'My PHP Application',
    'app.version' => '1.0.0',
    'app.debug' => false
]);

echo "應用程式配置:\n";
echo "- 名稱: " . file_service_config('app.name') . "\n";
echo "- 版本: " . file_service_config('app.version') . "\n";
echo "- 除錯: " . (file_service_config('app.debug') ? 'true' : 'false') . "\n";

echo "\n";

// 6. 配置檔案載入順序
echo "6. 配置檔案載入順序\n";

echo "系統會按以下順序搜尋配置檔案:\n";
echo "1. 專案根目錄的 config/file-service.php\n";
echo "2. 當前工作目錄的 config/file-service.php\n";
echo "3. 相對於入口檔案的 config/file-service.php\n";
echo "4. 套件預設配置\n\n";

// 7. 環境變數配置
echo "7. 環境變數配置\n";

// 設定一些環境變數
$_ENV['FILE_SERVICE_BASE_URL'] = 'https://production.api.com';
$_ENV['FILE_SERVICE_TIMEOUT'] = '45';
$_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';

echo "設定環境變數:\n";
echo "- FILE_SERVICE_BASE_URL=https://production.api.com\n";
echo "- FILE_SERVICE_TIMEOUT=45\n";
echo "- FILE_SERVICE_SSL_VERIFY=true\n";

// 重新載入配置以應用環境變數
$configWithEnv = GlobalConfigManager::loadConfig();
echo "\n環境變數覆蓋後的配置:\n";
echo "- Base URL: " . $configWithEnv['baseUrl'] . "\n";
echo "- Timeout: " . $configWithEnv['http']['timeout'] . " 秒\n";
echo "- SSL Verify: " . ($configWithEnv['http']['verify'] ? 'true' : 'false') . "\n";

echo "\n";

// 8. 儲存管理器使用
echo "8. 儲存管理器使用\n";

try {
    $storageConfig = [
        'baseUrl' => 'https://storage.example.com',
        'upload' => [
            'max_file_size' => 50 * 1024 * 1024, // 50MB
            'allowed_mime_types' => ['image/jpeg', 'image/png', 'application/pdf']
        ]
    ];
    
    $storage = new StorageManager($storageConfig);
    echo "✓ 儲存管理器創建成功\n";
    echo "- 最大檔案大小: " . number_format($storageConfig['upload']['max_file_size'] / 1024 / 1024, 2) . " MB\n";
    echo "- 允許的檔案類型: " . implode(', ', $storageConfig['upload']['allowed_mime_types']) . "\n";
    
} catch (Exception $e) {
    echo "儲存管理器創建失敗: " . $e->getMessage() . "\n";
}

echo "\n";

// 9. 錯誤處理最佳實踐
echo "9. 錯誤處理最佳實踐\n";

echo "建議的錯誤處理方式:\n";
echo "```php\n";
echo "try {\n";
echo "    \$client = new FileServiceClient();\n";
echo "    // 使用客戶端...\n";
echo "} catch (ConfigurationException \$e) {\n";
echo "    // 配置相關錯誤\n";
echo "    error_log('配置錯誤: ' . \$e->getMessage());\n";
echo "} catch (FileServiceException \$e) {\n";
echo "    // 一般服務錯誤\n";
echo "    error_log('服務錯誤: ' . \$e->getMessage());\n";
echo "} catch (Exception \$e) {\n";
echo "    // 其他未預期錯誤\n";
echo "    error_log('未知錯誤: ' . \$e->getMessage());\n";
echo "}\n";
echo "```\n\n";

// 10. 效能優化建議
echo "10. 效能優化建議\n";

echo "為了獲得最佳效能，建議:\n";
echo "1. 在應用程式啟動時載入配置\n";
echo "2. 避免在迴圈中重複載入配置\n";
echo "3. 使用環境變數進行環境特定的配置\n";
echo "4. 考慮實現簡單的配置快取機制\n\n";

// 示範配置快取
$cacheFile = sys_get_temp_dir() . '/file_service_config_cache.php';
echo "配置快取示例 (快取檔案: {$cacheFile}):\n";

if (!file_exists($cacheFile)) {
    $config = GlobalConfigManager::all();
    $cacheContent = "<?php\nreturn " . var_export($config, true) . ";\n";
    file_put_contents($cacheFile, $cacheContent);
    echo "✓ 配置已快取到檔案\n";
} else {
    $cachedConfig = require $cacheFile;
    echo "✓ 從快取載入配置 (共 " . count($cachedConfig) . " 個項目)\n";
}

echo "\n";

// 11. 向後相容性
echo "11. 向後相容性\n";

echo "如果您已經在使用舊版的 ConfigManager，可以繼續使用:\n";

try {
    // 使用舊版 API
    $oldConfig = ConfigManager::get('baseUrl');
    echo "✓ 舊版 ConfigManager::get() 仍然可用: {$oldConfig}\n";
    
    $allOldConfig = ConfigManager::all();
    echo "✓ 舊版 ConfigManager::all() 返回 " . count($allOldConfig) . " 個配置項目\n";
    
    // 檢查是否啟用了新的全域配置系統
    $isGlobalEnabled = ConfigManager::isGlobalConfigEnabled();
    echo "✓ 新的全域配置系統: " . ($isGlobalEnabled ? '已啟用' : '已禁用') . "\n";
    
} catch (Exception $e) {
    echo "✗ 向後相容性測試失敗: " . $e->getMessage() . "\n";
}

echo "\n";

// 12. 清理
echo "12. 清理\n";

// 清理快取檔案
if (file_exists($cacheFile)) {
    unlink($cacheFile);
    echo "✓ 清理快取檔案\n";
}

// 清理範例配置目錄
if (is_dir($configDir)) {
    $files = glob($configDir . '/*');
    foreach ($files as $file) {
        unlink($file);
    }
    rmdir($configDir);
    echo "✓ 清理範例配置目錄\n";
}

// 清理環境變數
unset($_ENV['FILE_SERVICE_BASE_URL']);
unset($_ENV['FILE_SERVICE_TIMEOUT']);
unset($_ENV['FILE_SERVICE_SSL_VERIFY']);

echo "✓ 清理環境變數\n";

// 清理運行時配置
GlobalConfigManager::clearRuntimeConfig();
ConfigManager::clearCache();

echo "✓ 清理運行時配置\n";

echo "\n=== 原生 PHP 專案使用範例結束 ===\n";

echo "\n總結:\n";
echo "原生 PHP 專案中使用 File Service Client 非常簡單:\n";
echo "1. 安裝套件: composer require fdmc/file-service-client\n";
echo "2. 載入自動載入器: require_once 'vendor/autoload.php'\n";
echo "3. 創建客戶端: \$client = new FileServiceClient()\n";
echo "4. 或使用全域函數: file_service_config('baseUrl')\n";
echo "5. 根據需要配置環境變數或配置檔案\n";
echo "\n享受使用 File Service Client! 🚀\n";
