<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Processing;

/**
 * 驗證結果類別
 * 
 * 封裝配置驗證的結果，包含驗證狀態、錯誤訊息和修復建議。
 */
class ValidationResult
{
    /**
     * @var bool 驗證是否通過
     */
    private bool $isValid;

    /**
     * @var array 錯誤列表
     */
    private array $errors;

    /**
     * @param bool $isValid 驗證是否通過
     * @param array $errors 錯誤列表
     */
    public function __construct(bool $isValid, array $errors = [])
    {
        $this->isValid = $isValid;
        $this->errors = $errors;
    }

    /**
     * 檢查驗證是否通過
     *
     * @return bool 驗證是否通過
     */
    public function isValid(): bool
    {
        return $this->isValid;
    }

    /**
     * 檢查驗證是否失敗
     *
     * @return bool 驗證是否失敗
     */
    public function hasErrors(): bool
    {
        return !$this->isValid;
    }

    /**
     * 獲取所有錯誤
     *
     * @return array 錯誤列表
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * 獲取特定配置鍵的錯誤
     *
     * @param string $key 配置鍵
     * @return array 該配置鍵的錯誤列表
     */
    public function getErrorsForKey(string $key): array
    {
        return array_values(array_filter($this->errors, fn($error) => $error['key'] === $key));
    }

    /**
     * 獲取特定類型的錯誤
     *
     * @param string $type 錯誤類型
     * @return array 該類型的錯誤列表
     */
    public function getErrorsByType(string $type): array
    {
        return array_values(array_filter($this->errors, fn($error) => $error['type'] === $type));
    }

    /**
     * 獲取錯誤數量
     *
     * @return int 錯誤數量
     */
    public function getErrorCount(): int
    {
        return count($this->errors);
    }

    /**
     * 獲取格式化的錯誤訊息
     *
     * @param bool $includeSuggestions 是否包含修復建議
     * @return array 格式化的錯誤訊息列表
     */
    public function getFormattedErrors(bool $includeSuggestions = true): array
    {
        $formatted = [];

        foreach ($this->errors as $error) {
            $message = "[{$error['key']}] {$error['message']}";

            if (!empty($error['description'])) {
                $message .= " ({$error['description']})";
            }

            if ($includeSuggestions && !empty($error['suggestion'])) {
                $message .= "\n  建議：{$error['suggestion']}";
            }

            $formatted[] = $message;
        }

        return $formatted;
    }

    /**
     * 獲取錯誤摘要
     *
     * @return string 錯誤摘要
     */
    public function getSummary(): string
    {
        if ($this->isValid) {
            return "配置驗證通過";
        }

        $errorCount = $this->getErrorCount();
        $errorTypes = array_unique(array_column($this->errors, 'type'));

        return "配置驗證失敗：發現 {$errorCount} 個錯誤，類型：" . implode(', ', $errorTypes);
    }

    /**
     * 獲取修復建議摘要
     *
     * @return array 修復建議列表
     */
    public function getRepairSuggestions(): array
    {
        $suggestions = [];

        foreach ($this->errors as $error) {
            if (!empty($error['suggestion'])) {
                $suggestions[$error['key']] = $error['suggestion'];
            }
        }

        return $suggestions;
    }

    /**
     * 轉換為陣列格式
     *
     * @return array 驗證結果陣列
     */
    public function toArray(): array
    {
        return [
            'is_valid' => $this->isValid,
            'error_count' => $this->getErrorCount(),
            'errors' => $this->errors,
            'summary' => $this->getSummary(),
            'suggestions' => $this->getRepairSuggestions(),
        ];
    }

    /**
     * 轉換為 JSON 格式
     *
     * @param int $flags JSON 編碼選項
     * @return string JSON 字串
     */
    public function toJson(int $flags = JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE): string
    {
        return json_encode($this->toArray(), $flags);
    }

    /**
     * 輸出詳細的驗證報告
     *
     * @param bool $includeSuggestions 是否包含修復建議
     * @return string 驗證報告
     */
    public function getDetailedReport(bool $includeSuggestions = true): string
    {
        $report = [];
        $report[] = "=== 配置驗證報告 ===";
        $report[] = $this->getSummary();
        $report[] = "";

        if ($this->hasErrors()) {
            $report[] = "錯誤詳情：";
            foreach ($this->getFormattedErrors($includeSuggestions) as $error) {
                $report[] = "  • " . $error;
            }

            if ($includeSuggestions) {
                $suggestions = $this->getRepairSuggestions();
                if (!empty($suggestions)) {
                    $report[] = "";
                    $report[] = "修復建議摘要：";
                    foreach ($suggestions as $key => $suggestion) {
                        $report[] = "  [{$key}] {$suggestion}";
                    }
                }
            }
        } else {
            $report[] = "所有配置項目驗證通過！";
        }

        return implode("\n", $report);
    }
}