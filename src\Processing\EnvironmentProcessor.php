<?php

namespace FDMC\FileServiceClient\Processing;

use FDMC\FileServiceClient\Exceptions\TypeConversionException;

/**
 * 環境變數處理器
 * 負責處理環境變數的讀取、型別轉換和配置覆蓋
 */
class EnvironmentProcessor
{
    /**
     * 環境變數與配置鍵的對應關係
     */
    private const ENV_CONFIG_MAP = [
        'FILE_SERVICE_BASE_URL' => 'baseUrl',
        'FILE_SERVICE_TIMEOUT' => 'http.timeout',
        'FILE_SERVICE_CONNECT_TIMEOUT' => 'http.connect_timeout',
        'FILE_SERVICE_READ_TIMEOUT' => 'http.read_timeout',
        'FILE_SERVICE_SSL_VERIFY' => 'http.verify',
        'FILE_SERVICE_UPLOAD_TIMEOUT' => 'upload.timeout',
        'FILE_SERVICE_MAX_FILE_SIZE' => 'upload.max_file_size',
        'FILE_SERVICE_DOWNLOAD_TIMEOUT' => 'download.timeout',
        'FILE_SERVICE_CACHE_ENABLED' => 'cache.enabled',
        'FILE_SERVICE_CACHE_DRIVER' => 'cache.driver',
        'FILE_SERVICE_CACHE_TTL' => 'cache.ttl',
        'FILE_SERVICE_LOG_ENABLED' => 'logging.enabled',
        'FILE_SERVICE_LOG_LEVEL' => 'logging.level',
        'FILE_SERVICE_LOG_CHANNEL' => 'logging.channel',
        'FILE_SERVICE_RETRY_MAX_ATTEMPTS' => 'retry.max_attempts',
        'FILE_SERVICE_RETRY_DELAY' => 'retry.delay',
        'FILE_SERVICE_RETRY_MULTIPLIER' => 'retry.multiplier',
    ];

    /**
     * 配置項目的預期型別
     */
    private const CONFIG_TYPES = [
        'baseUrl' => 'string',
        'http.timeout' => 'integer',
        'http.connect_timeout' => 'integer',
        'http.read_timeout' => 'integer',
        'http.verify' => 'boolean',
        'upload.timeout' => 'integer',
        'upload.max_file_size' => 'integer',
        'download.timeout' => 'integer',
        'cache.enabled' => 'boolean',
        'cache.driver' => 'string',
        'cache.ttl' => 'integer',
        'logging.enabled' => 'boolean',
        'logging.level' => 'string',
        'logging.channel' => 'string',
        'retry.max_attempts' => 'integer',
        'retry.delay' => 'integer',
        'retry.multiplier' => 'integer',
    ];

    /**
     * 處理環境變數覆蓋配置
     * 
     * @param array $config 基礎配置
     * @return array 處理後的配置
     */
    public function processEnvVars(array $config): array
    {
        foreach (self::ENV_CONFIG_MAP as $envKey => $configKey) {
            $envValue = $this->getEnvironmentVariable($envKey);

            if ($envValue !== null) {
                try {
                    $processedValue = $this->processEnvironmentVariable($configKey, $envValue);
                    $this->setNestedValue($config, $configKey, $processedValue);
                } catch (TypeConversionException $e) {
                    // 記錄警告並忽略無效的環境變數（保持原配置值）
                    error_log("環境變數 {$envKey} 型別轉換失敗: {$e->getMessage()}，忽略該環境變數");
                    // 不設定配置值，保持原有值
                }
            }
        }

        return $config;
    }

    /**
     * 提取環境變數覆蓋配置
     * 返回僅包含環境變數覆蓋的配置陣列
     * 
     * @return array 環境變數覆蓋配置
     */
    public function extractEnvOverrides(): array
    {
        $overrides = [];

        foreach (self::ENV_CONFIG_MAP as $envKey => $configKey) {
            $envValue = $this->getEnvironmentVariable($envKey);

            if ($envValue !== null) {
                try {
                    $processedValue = $this->processEnvironmentVariable($configKey, $envValue);
                    $this->setNestedValue($overrides, $configKey, $processedValue);
                } catch (TypeConversionException $e) {
                    // 記錄警告並忽略無效的環境變數
                    error_log("環境變數 {$envKey} 型別轉換失敗: {$e->getMessage()}，忽略該環境變數");
                    // 不設定覆蓋值，保持原有值
                }
            }
        }

        return $overrides;
    }

    /**
     * 安全的環境變數型別轉換
     * 
     * @param string $configKey 配置鍵
     * @param string $value 環境變數值
     * @return mixed 轉換後的值
     * @throws TypeConversionException 型別轉換失敗時拋出例外
     */
    public function processEnvironmentVariable(string $configKey, string $value)
    {
        $expectedType = $this->getExpectedType($configKey);

        return match ($expectedType) {
            'boolean' => $this->parseBoolean($value, $configKey),
            'integer' => $this->parseInteger($value, $configKey),
            'float' => $this->parseFloat($value, $configKey),
            'array' => $this->parseArray($value, $configKey),
            'string' => $this->parseString($value),
            default => $value
        };
    }

    /**
     * 嚴格的布林值解析
     * 
     * @param string $value 要解析的值
     * @param string $configKey 配置鍵（用於錯誤報告）
     * @return bool 解析後的布林值
     * @throws TypeConversionException 解析失敗時拋出例外
     */
    public function parseBoolean(string $value, string $configKey = ''): bool
    {
        $normalized = strtolower(trim($value));

        if (in_array($normalized, ['true', '1', 'yes', 'on'], true)) {
            return true;
        }

        if (in_array($normalized, ['false', '0', 'no', 'off', ''], true)) {
            return false;
        }

        throw TypeConversionException::booleanConversionFailed($configKey, $value);
    }

    /**
     * 整數解析與驗證
     * 
     * @param string $value 要解析的值
     * @param string $configKey 配置鍵（用於錯誤報告）
     * @return int 解析後的整數
     * @throws TypeConversionException 解析失敗時拋出例外
     */
    public function parseInteger(string $value, string $configKey = ''): int
    {
        $trimmed = trim($value);

        if (!is_numeric($trimmed) || strpos($trimmed, '.') !== false) {
            throw TypeConversionException::integerConversionFailed($configKey, $value);
        }

        $intValue = (int) $trimmed;

        // 檢查是否在合理範圍內
        if ($intValue < 0) {
            throw TypeConversionException::integerConversionFailed($configKey, $value);
        }

        return $intValue;
    }

    /**
     * 浮點數解析與驗證
     * 
     * @param string $value 要解析的值
     * @param string $configKey 配置鍵（用於錯誤報告）
     * @return float 解析後的浮點數
     * @throws TypeConversionException 解析失敗時拋出例外
     */
    public function parseFloat(string $value, string $configKey = ''): float
    {
        $trimmed = trim($value);

        if (!is_numeric($trimmed)) {
            throw TypeConversionException::floatConversionFailed($configKey, $value);
        }

        return (float) $trimmed;
    }

    /**
     * JSON 陣列解析
     * 
     * @param string $value 要解析的 JSON 字串
     * @param string $configKey 配置鍵（用於錯誤報告）
     * @return array 解析後的陣列
     * @throws TypeConversionException 解析失敗時拋出例外
     */
    public function parseArray(string $value, string $configKey = ''): array
    {
        $trimmed = trim($value);

        if (empty($trimmed)) {
            return [];
        }

        $decoded = json_decode($trimmed, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw TypeConversionException::arrayConversionFailed($configKey, $value, json_last_error_msg());
        }

        if (!is_array($decoded)) {
            throw TypeConversionException::arrayConversionFailed($configKey, $value, 'JSON 解析結果不是陣列');
        }

        return $decoded;
    }

    /**
     * 字串解析（基本清理）
     * 
     * @param string $value 要解析的值
     * @return string 解析後的字串
     */
    public function parseString(string $value): string
    {
        return trim($value);
    }

    /**
     * 獲取配置項目的預期型別
     * 
     * @param string $configKey 配置鍵
     * @return string 預期型別
     */
    public function getExpectedType(string $configKey): string
    {
        return self::CONFIG_TYPES[$configKey] ?? 'string';
    }

    /**
     * 獲取環境變數值
     * 
     * @param string $key 環境變數鍵
     * @return string|null 環境變數值
     */
    private function getEnvironmentVariable(string $key): ?string
    {
        // 優先從 $_ENV 獲取
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }

        // 其次從 $_SERVER 獲取
        if (isset($_SERVER[$key])) {
            return $_SERVER[$key];
        }

        // 最後使用 getenv 函數
        $value = getenv($key);
        return $value !== false ? $value : null;
    }

    /**
     * 設定巢狀陣列的值
     * 
     * @param array $array 陣列（引用傳遞）
     * @param string $key 鍵，支援點號分隔
     * @param mixed $value 值
     * @return void
     */
    private function setNestedValue(array &$array, string $key, $value): void
    {
        if (strpos($key, '.') === false) {
            $array[$key] = $value;
            return;
        }

        $keys = explode('.', $key);
        $current = &$array;

        foreach ($keys as $nestedKey) {
            if (!isset($current[$nestedKey]) || !is_array($current[$nestedKey])) {
                $current[$nestedKey] = [];
            }
            $current = &$current[$nestedKey];
        }

        $current = $value;
    }
}