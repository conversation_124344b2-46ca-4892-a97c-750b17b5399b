<?php

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\Core\ConfigLoader;
use FDMC\FileServiceClient\Exceptions\FileServiceException;
use PHPUnit\Framework\TestCase;

/**
 * ConfigLoader 測試類別
 * 測試配置載入器的各項功能
 */
class ConfigLoaderTest extends TestCase
{
    private ConfigLoader $configLoader;
    private string $tempDir;

    protected function setUp(): void
    {
        $this->configLoader = new ConfigLoader();
        $this->tempDir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'config_loader_test_' . uniqid();
        mkdir($this->tempDir, 0755, true);
    }

    protected function tearDown(): void
    {
        $this->removeDirectory($this->tempDir);
    }

    /**
     * 測試從有效配置檔案載入
     */
    public function testLoadFromValidConfigFile(): void
    {
        $configPath = $this->createTempConfigFile([
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 60,
            ],
        ]);

        $config = $this->configLoader->loadFromFile($configPath);

        $this->assertIsArray($config);
        $this->assertEquals('https://api.example.com', $config['baseUrl']);
        $this->assertEquals(60, $config['http']['timeout']);
    }

    /**
     * 測試載入不存在的配置檔案時使用預設配置
     */
    public function testLoadFromNonExistentFileUsesDefaults(): void
    {
        $config = $this->configLoader->loadFromFile('/non/existent/path.php');

        $this->assertIsArray($config);
        $this->assertEquals('http://localhost:8080', $config['baseUrl']);
    }

    /**
     * 測試專案根目錄偵測
     */
    public function testFindProjectRoot(): void
    {
        // 建立模擬的專案結構
        $projectDir = $this->tempDir . DIRECTORY_SEPARATOR . 'project';
        mkdir($projectDir, 0755, true);

        $composerJson = [
            'name' => 'test/project',
            'require' => [
                'php' => '^8.0'
            ]
        ];
        file_put_contents($projectDir . DIRECTORY_SEPARATOR . 'composer.json', json_encode($composerJson));

        // 建立子目錄
        $subDir = $projectDir . DIRECTORY_SEPARATOR . 'src';
        mkdir($subDir, 0755, true);

        // 從子目錄開始搜尋
        $originalCwd = getcwd();
        chdir($subDir);

        try {
            $foundRoot = $this->configLoader->findProjectRoot();
            $this->assertEquals(realpath($projectDir), realpath($foundRoot));
        } finally {
            chdir($originalCwd);
        }
    }

    /**
     * 測試配置檔案搜尋路徑
     */
    public function testGetConfigSearchPaths(): void
    {
        $paths = $this->configLoader->getConfigSearchPaths();

        $this->assertIsArray($paths);
        $this->assertNotEmpty($paths);

        // 檢查是否包含套件預設配置路徑
        $packageConfigFound = false;
        foreach ($paths as $path) {
            if (
                str_contains($path, 'config' . DIRECTORY_SEPARATOR . 'file-service.php') ||
                str_contains($path, 'config/file-service.php')
            ) {
                $packageConfigFound = true;
                break;
            }
        }
        $this->assertTrue($packageConfigFound, '搜尋路徑應包含套件預設配置。實際路徑: ' . implode(', ', $paths));
    }

    /**
     * 測試配置檔案驗證 - 有效檔案
     */
    public function testValidateConfigFileValid(): void
    {
        $configPath = $this->createTempConfigFile(['baseUrl' => 'http://localhost']);

        $this->expectNotToPerformAssertions();
        $this->configLoader->validateConfigFile($configPath);
    }

    /**
     * 測試配置檔案驗證 - 檔案不存在
     */
    public function testValidateConfigFileNotExists(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('配置檔案不存在');

        $this->configLoader->validateConfigFile('/non/existent/file.php');
    }

    /**
     * 測試配置檔案驗證 - 不是檔案
     */
    public function testValidateConfigFileNotFile(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('配置路徑不是檔案');

        $this->configLoader->validateConfigFile($this->tempDir);
    }

    /**
     * 測試配置檔案驗證 - 檔案過大
     */
    public function testValidateConfigFileTooLarge(): void
    {
        $largePath = $this->tempDir . DIRECTORY_SEPARATOR . 'large.php';
        $largeContent = '<?php return ' . str_repeat('["data" => "' . str_repeat('x', 1000) . '"],', 2000) . '[];';
        file_put_contents($largePath, $largeContent);

        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('配置檔案過大');

        $this->configLoader->validateConfigFile($largePath);
    }

    /**
     * 測試配置格式驗證 - 有效配置
     */
    public function testValidateConfigValid(): void
    {
        $config = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 30,
                'verify' => true,
            ],
            'logging' => [
                'level' => 'info',
                'enabled' => true,
            ],
        ];

        $this->expectNotToPerformAssertions();
        $this->configLoader->validateConfig($config, 'test.php');
    }

    /**
     * 測試配置格式驗證 - 非陣列
     */
    public function testValidateConfigNotArray(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('配置檔案必須返回陣列格式');

        $this->configLoader->validateConfig('not an array', 'test.php');
    }

    /**
     * 測試配置格式驗證 - 缺少必要欄位
     */
    public function testValidateConfigMissingRequired(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('配置檔案缺少必要欄位');

        $this->configLoader->validateConfig([], 'test.php');
    }

    /**
     * 測試配置格式驗證 - 無效 URL
     */
    public function testValidateConfigInvalidUrl(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('格式不正確，必須為有效的 URL');

        $config = ['baseUrl' => 'not-a-valid-url'];
        $this->configLoader->validateConfig($config, 'test.php');
    }

    /**
     * 測試配置格式驗證 - 數值範圍錯誤
     */
    public function testValidateConfigNumericRange(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('小於最小值');

        $config = [
            'baseUrl' => 'http://localhost',
            'http' => ['timeout' => 0], // 小於最小值 1
        ];
        $this->configLoader->validateConfig($config, 'test.php');
    }

    /**
     * 測試配置格式驗證 - 無效枚舉值
     */
    public function testValidateConfigInvalidEnum(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage('不在允許的值範圍內');

        $config = [
            'baseUrl' => 'http://localhost',
            'logging' => ['level' => 'invalid_level'],
        ];
        $this->configLoader->validateConfig($config, 'test.php');
    }

    /**
     * 測試與預設配置合併
     */
    public function testMergeWithDefaults(): void
    {
        $userConfig = [
            'baseUrl' => 'https://custom.api.com',
            'http' => [
                'timeout' => 60,
            ],
        ];

        $merged = $this->configLoader->mergeWithDefaults($userConfig);

        $this->assertEquals('https://custom.api.com', $merged['baseUrl']);
        $this->assertEquals(60, $merged['http']['timeout']);
        $this->assertEquals(10, $merged['http']['connect_timeout']); // 來自預設配置
    }

    /**
     * 建立臨時配置檔案
     */
    private function createTempConfigFile(array $config): string
    {
        $configPath = $this->tempDir . DIRECTORY_SEPARATOR . 'config.php';
        $content = '<?php return ' . var_export($config, true) . ';';
        file_put_contents($configPath, $content);
        return $configPath;
    }

    /**
     * 遞迴刪除目錄
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}