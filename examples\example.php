<?php

require_once 'vendor/autoload.php';

use Fdmc\FileServiceClient\FileServiceClient;
use Fdmc\FileServiceClient\StorageManager;
use Fdmc\FileServiceClient\FileServiceException;

// 初始化客戶端
$client = new FileServiceClient('http://localhost:8000'); // 替換為實際的 file-service URL

// 初始化儲存管理器
$storageManager = new StorageManager('http://localhost:8000');

// 基本參數
$companyId = 1;
$scope = 'document';
$userId = 123;

try {
    // ===== 物件操作示例 =====

    // 1. 列出根目錄下的所有物件
    echo "=== 列出根目錄物件 ===\n";
    $rootObjects = $client->listRootObjects($companyId, $scope, $userId);
    print_r($rootObjects);

    // 2. 創建資料夾
    echo "\n=== 創建資料夾 ===\n";
    $folderData = $client->buildObjectData(
        company_id: $companyId,
        scope: $scope,
        name: '測試資料夾',
        type: 'folder'
    );
    $folder = $client->createObject($userId, $folderData);
    print_r($folder);

    // 3. 創建檔案
    echo "\n=== 創建檔案 ===\n";
    $fileData = $client->buildObjectData(
        company_id: $companyId,
        scope: $scope,
        name: 'test.txt',
        type: 'file',
        parent: $folder['data']['id'] ?? null,
        storage_path: '/tmp/test.txt',
        mimeType: 'text/plain',
        size: 1024,
        sha1: 'abc123',
        md5: 'def456'
    );
    $file = $client->createObject($userId, $fileData);
    print_r($file);

    // 4. 檢查檔案是否存在
    if (isset($file['data']['id'])) {
        echo "\n=== 檢查檔案存在性 ===\n";
        $exists = $client->checkObjectExists($file['data']['id'], $userId);
        print_r($exists);

        // 5. 獲取檔案資訊
        echo "\n=== 獲取檔案資訊 ===\n";
        $fileInfo = $client->getObjectInfo($file['data']['id'], $userId);
        print_r($fileInfo);

        // 6. 重命名檔案
        echo "\n=== 重命名檔案 ===\n";
        $rename = $client->renameObject($file['data']['id'], $userId, 'renamed_test.txt');
        print_r($rename);
    }

    // ===== 版本管理示例 =====
    if (isset($file['data']['id'])) {
        echo "\n=== 獲取版本列表 ===\n";
        $versions = $client->getVersionList($file['data']['id'], $userId);
        print_r($versions);
    }

    // ===== 搜尋示例 =====
    echo "\n=== 搜尋檔案 ===\n";
    $searchResults = $client->search($companyId, $scope, $userId, 'test');
    print_r($searchResults);

    // ===== 儲存管理示例 =====
    echo "\n=== 列出所有儲存 ===\n";
    $storages = $storageManager->listStorages($userId);
    print_r($storages);

    // 創建新儲存
    echo "\n=== 創建新儲存 ===\n";
    $storageData = $storageManager->buildStorageData(
        name: '測試儲存',
        type: 'FS_LOCAL',
        host: 'localhost',
        remark: '本地測試儲存',
        payload: ['path' => '/var/storage']
    );
    $storage = $storageManager->createStorage($userId, $storageData);
    print_r($storage);

} catch (FileServiceException $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
    echo "錯誤代碼: " . $e->getCode() . "\n";
} catch (Exception $e) {
    echo "未預期的錯誤: " . $e->getMessage() . "\n";
}