<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\Core\ConfigMerger;
use PHPUnit\Framework\TestCase;

/**
 * 配置合併器測試
 */
class ConfigMergerTest extends TestCase
{
    private ConfigMerger $merger;

    protected function setUp(): void
    {
        $this->merger = new ConfigMerger();
    }

    /**
     * 測試基本的遞迴合併
     */
    public function testMergeRecursive(): void
    {
        $base = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'verify' => false,
            ],
            'headers' => [
                'Content-Type' => 'application/json',
            ],
        ];

        $override = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 60,
                'connect_timeout' => 10,
            ],
            'headers' => [
                'Authorization' => 'Bearer token',
            ],
        ];

        $expected = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 60,
                'verify' => false,
                'connect_timeout' => 10,
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer token',
            ],
        ];

        $result = $this->merger->mergeRecursive($base, $override);
        $this->assertEquals($expected, $result);
    }

    /**
     * 測試配置優先級合併
     */
    public function testMergeConfigs(): void
    {
        $configs = [
            ['priority' => ConfigMerger::PRIORITY_DEFAULT, 'config' => ['timeout' => 30, 'verify' => false]],
            ['priority' => ConfigMerger::PRIORITY_ENVIRONMENT, 'config' => ['timeout' => 60]],
            ['priority' => ConfigMerger::PRIORITY_USER_OPTIONS, 'config' => ['verify' => true]],
        ];

        $expected = [
            'timeout' => 60,  // 環境變數覆蓋預設值
            'verify' => true, // 使用者選項覆蓋預設值
        ];

        $result = $this->merger->mergeConfigs($configs);
        $this->assertEquals($expected, $result);
    }

    /**
     * 測試使用者選項合併
     */
    public function testMergeUserOptions(): void
    {
        $defaultConfig = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'verify' => false,
            ],
        ];

        $userOptions = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 60,
            ],
        ];

        $expected = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 60,
                'verify' => false,
            ],
        ];

        $result = $this->merger->mergeUserOptions($defaultConfig, $userOptions);
        $this->assertEquals($expected, $result);
    }

    /**
     * 測試環境變數覆蓋
     */
    public function testApplyEnvironmentOverrides(): void
    {
        $config = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
            ],
        ];

        $envOverrides = [
            'baseUrl' => 'https://env.example.com',
            'http' => [
                'verify' => true,
            ],
        ];

        $expected = [
            'baseUrl' => 'https://env.example.com',
            'http' => [
                'timeout' => 30,
                'verify' => true,
            ],
        ];

        $result = $this->merger->applyEnvironmentOverrides($config, $envOverrides);
        $this->assertEquals($expected, $result);
    }

    /**
     * 測試完整的配置合併流程
     */
    public function testMergeAllConfigs(): void
    {
        $defaultConfig = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'verify' => false,
            ],
        ];

        $projectConfig = [
            'baseUrl' => 'https://project.example.com',
            'http' => [
                'timeout' => 45,
            ],
        ];

        $envOverrides = [
            'http' => [
                'verify' => true,
            ],
        ];

        $userOptions = [
            'http' => [
                'connect_timeout' => 10,
            ],
        ];

        $runtimeConfig = [
            'baseUrl' => 'https://runtime.example.com',
        ];

        $expected = [
            'baseUrl' => 'https://runtime.example.com', // 運行時配置優先級最高
            'http' => [
                'timeout' => 45,           // 專案配置
                'verify' => true,          // 環境變數覆蓋
                'connect_timeout' => 10,   // 使用者選項
            ],
        ];

        $result = $this->merger->mergeAllConfigs(
            $defaultConfig,
            $projectConfig,
            $envOverrides,
            $userOptions,
            $runtimeConfig
        );

        $this->assertEquals($expected, $result);
    }

    /**
     * 測試智慧陣列合併
     */
    public function testSmartArrayMerge(): void
    {
        // 測試索引陣列合併
        $base = ['item1', 'item2'];
        $override = ['item2', 'item3'];
        $expected = ['item1', 'item2', 'item3'];

        $result = $this->merger->smartArrayMerge($base, $override);
        $this->assertEquals($expected, $result);

        // 測試關聯陣列合併
        $base = ['key1' => 'value1', 'key2' => 'value2'];
        $override = ['key2' => 'new_value2', 'key3' => 'value3'];
        $expected = ['key1' => 'value1', 'key2' => 'new_value2', 'key3' => 'value3'];

        $result = $this->merger->smartArrayMerge($base, $override);
        $this->assertEquals($expected, $result);

        // 測試特殊鍵的完全替換
        $base = ['Content-Type' => 'application/json'];
        $override = ['Authorization' => 'Bearer token'];
        $expected = ['Authorization' => 'Bearer token'];

        $result = $this->merger->smartArrayMerge($base, $override, 'headers');
        $this->assertEquals($expected, $result);
    }

    /**
     * 測試索引陣列檢測
     */
    public function testIsIndexedArray(): void
    {
        $this->assertTrue($this->merger->isIndexedArray([]));
        $this->assertTrue($this->merger->isIndexedArray([1, 2, 3]));
        $this->assertTrue($this->merger->isIndexedArray(['a', 'b', 'c']));

        $this->assertFalse($this->merger->isIndexedArray(['key' => 'value']));
        $this->assertFalse($this->merger->isIndexedArray([1 => 'a', 2 => 'b']));
        $this->assertFalse($this->merger->isIndexedArray(['a' => 1, 'b' => 2]));
    }

    /**
     * 測試深度複製
     */
    public function testDeepCopy(): void
    {
        $original = [
            'level1' => [
                'level2' => [
                    'value' => 'test',
                ],
            ],
        ];

        $copy = $this->merger->deepCopy($original);

        // 修改複製的陣列不應影響原始陣列
        $copy['level1']['level2']['value'] = 'modified';

        $this->assertEquals('test', $original['level1']['level2']['value']);
        $this->assertEquals('modified', $copy['level1']['level2']['value']);
    }

    /**
     * 測試配置驗證
     */
    public function testValidateMergedConfig(): void
    {
        $validConfig = [
            'baseUrl' => 'https://api.example.com',
            'http' => ['timeout' => 30],
        ];

        $invalidConfig = [
            'http' => ['timeout' => 30],
            // 缺少 baseUrl
        ];

        $this->assertTrue($this->merger->validateMergedConfig($validConfig));
        $this->assertFalse($this->merger->validateMergedConfig($invalidConfig));
    }

    /**
     * 測試巢狀鍵檢查
     */
    public function testHasNestedKey(): void
    {
        $config = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 30,
                'options' => [
                    'verify' => false,
                ],
            ],
        ];

        $this->assertTrue($this->merger->hasNestedKey($config, 'baseUrl'));
        $this->assertTrue($this->merger->hasNestedKey($config, 'http.timeout'));
        $this->assertTrue($this->merger->hasNestedKey($config, 'http.options.verify'));

        $this->assertFalse($this->merger->hasNestedKey($config, 'nonexistent'));
        $this->assertFalse($this->merger->hasNestedKey($config, 'http.nonexistent'));
        $this->assertFalse($this->merger->hasNestedKey($config, 'http.options.nonexistent'));
    }

    /**
     * 測試配置差異檢測
     */
    public function testGetConfigDiff(): void
    {
        $before = [
            'baseUrl' => 'http://localhost:8080',
            'http' => [
                'timeout' => 30,
                'verify' => false,
            ],
            'removed_key' => 'will_be_removed',
        ];

        $after = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 60,
                'verify' => false,
                'connect_timeout' => 10,
            ],
            'new_key' => 'added',
        ];

        $diff = $this->merger->getConfigDiff($before, $after);

        $this->assertEquals('added', $diff['added']['new_key']);
        $this->assertEquals('will_be_removed', $diff['removed']['removed_key']);
        $this->assertEquals('https://api.example.com', $diff['modified']['baseUrl']['to']);
        $this->assertEquals('http://localhost:8080', $diff['modified']['baseUrl']['from']);
        $this->assertEquals(10, $diff['modified']['http']['added']['connect_timeout']);
        $this->assertEquals(60, $diff['modified']['http']['modified']['timeout']['to']);
        $this->assertEquals(30, $diff['modified']['http']['modified']['timeout']['from']);
    }

    /**
     * 測試空配置處理
     */
    public function testEmptyConfigHandling(): void
    {
        $result = $this->merger->mergeConfigs([]);
        $this->assertEquals([], $result);

        $result = $this->merger->mergeRecursive([], []);
        $this->assertEquals([], $result);

        $result = $this->merger->mergeAllConfigs();
        $this->assertEquals([], $result);
    }

    /**
     * 測試無效配置處理
     */
    public function testInvalidConfigHandling(): void
    {
        $configs = [
            ['priority' => 1, 'config' => 'invalid'], // 非陣列配置
            ['priority' => 2, 'config' => ['valid' => 'config']],
            ['priority' => 3], // 缺少 config 鍵
        ];

        $expected = ['valid' => 'config'];
        $result = $this->merger->mergeConfigs($configs);
        $this->assertEquals($expected, $result);
    }
}