<?php

namespace FDMC\FileServiceClient\Tests;

use PHPUnit\Framework\TestCase;
use FDMC\FileServiceClient\Client\StorageManager;
use FDMC\FileServiceClient\Exceptions\FileServiceException;

class StorageManagerTest extends TestCase
{
    private StorageManager $storageManager;

    protected function setUp(): void
    {
        // 模擬 env 函數
        if (!function_exists('env')) {
            eval ('
                function env($key, $default = null) {
                    return $_ENV[$key] ?? $default;
                }
            ');
        }

        $this->storageManager = new StorageManager(['baseUrl' => 'http://localhost:8000']);
    }

    public function testStorageManagerInitialization(): void
    {
        $this->assertInstanceOf(StorageManager::class, $this->storageManager);
        $this->assertEquals('http://localhost:8000', $this->storageManager->getBaseUrl());
    }

    public function testBuildStorageData(): void
    {
        $data = $this->storageManager->buildStorageData(
            name: 'Test Storage',
            type: 'FS_LOCAL'
        );

        $this->assertIsArray($data);
        $this->assertEquals('Test Storage', $data['name']);
        $this->assertEquals('FS_LOCAL', $data['type']);
    }

    public function testBuildStorageDataWithAllParameters(): void
    {
        $data = $this->storageManager->buildStorageData(
            name: 'Test Storage',
            type: 'FS_LOCAL',
            host: 'localhost',
            remark: 'Test remark',
            payload: ['path' => '/storage']
        );

        $this->assertEquals('localhost', $data['host']);
        $this->assertEquals('Test remark', $data['remark']);
        $this->assertEquals(['path' => '/storage'], $data['payload']);
    }

    public function testSetHeaders(): void
    {
        $this->storageManager->setHeaders(['Authorization' => 'Bearer token123']);

        // 由於 defaultHeaders 是私有屬性，我們只能測試方法不會拋出錯誤
        $this->assertTrue(true);
    }

    public function testStorageDataTypes(): void
    {
        $validTypes = ['S3', 'FTP', 'NAS', 'FS_LOCAL', 'AP_LOCAL'];

        foreach ($validTypes as $type) {
            $data = $this->storageManager->buildStorageData(
                name: "Test {$type} Storage",
                type: $type
            );

            $this->assertEquals($type, $data['type']);
        }
    }
}