<?php

/**
 * 環境變數配置範例
 * 
 * 展示如何使用環境變數來配置 File Service Client
 */

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\GlobalConfigManager;
use FDMC\FileServiceClient\EnvironmentProcessor;
use FDMC\FileServiceClient\FileServiceClient;

// 載入全域配置函數
require_once __DIR__ . '/../src/helpers.php';

// 模擬 env 函數（在實際使用中通常由框架提供）
if (!function_exists('env')) {
    function env($key, $default = null) {
        $value = $_ENV[$key] ?? getenv($key);
        if ($value === false) {
            return $default;
        }
        return $value;
    }
}

echo "=== 環境變數配置範例 ===\n\n";

// 1. 環境變數設定
echo "1. 設定環境變數\n";
echo "以下環境變數會被自動識別並應用到配置中：\n\n";

// 設定各種環境變數
$envVars = [
    'FILE_SERVICE_BASE_URL' => 'https://production.api.com',
    'FILE_SERVICE_TIMEOUT' => '45',
    'FILE_SERVICE_CONNECT_TIMEOUT' => '15', 
    'FILE_SERVICE_READ_TIMEOUT' => '90',
    'FILE_SERVICE_SSL_VERIFY' => 'true',
    'FILE_SERVICE_UPLOAD_TIMEOUT' => '180',
    'FILE_SERVICE_MAX_FILE_SIZE' => '157286400', // 150MB
    'FILE_SERVICE_DOWNLOAD_TIMEOUT' => '600',
    'FILE_SERVICE_CACHE_ENABLED' => 'true',
    'FILE_SERVICE_CACHE_DRIVER' => 'redis',
    'FILE_SERVICE_CACHE_TTL' => '7200',
    'FILE_SERVICE_LOG_ENABLED' => 'true',
    'FILE_SERVICE_LOG_LEVEL' => 'debug',
    'FILE_SERVICE_LOG_CHANNEL' => 'file-service',
    'FILE_SERVICE_RETRY_MAX_ATTEMPTS' => '5',
    'FILE_SERVICE_RETRY_DELAY' => '2000',
    'FILE_SERVICE_RETRY_MULTIPLIER' => '3',
];

foreach ($envVars as $key => $value) {
    $_ENV[$key] = $value;
    echo "設定 {$key}={$value}\n";
}

echo "\n";

// 2. 載入配置並展示環境變數的影響
echo "2. 載入配置並展示環境變數的影響\n";

$config = GlobalConfigManager::loadConfig();

echo "配置已載入，環境變數值已應用：\n";
echo "- Base URL: " . $config['baseUrl'] . "\n";
echo "- HTTP Timeout: " . $config['http']['timeout'] . " 秒\n";
echo "- Connect Timeout: " . $config['http']['connect_timeout'] . " 秒\n";
echo "- SSL Verify: " . ($config['http']['verify'] ? 'true' : 'false') . "\n";
echo "- Upload Timeout: " . $config['upload']['timeout'] . " 秒\n";
echo "- Max File Size: " . number_format($config['upload']['max_file_size'] / 1024 / 1024, 2) . " MB\n";
echo "- Cache Enabled: " . ($config['cache']['enabled'] ? 'true' : 'false') . "\n";
echo "- Cache TTL: " . $config['cache']['ttl'] . " 秒\n";
echo "- Log Level: " . $config['logging']['level'] . "\n";
echo "- Retry Attempts: " . $config['retry']['max_attempts'] . "\n\n";

// 3. 型別轉換示範
echo "3. 環境變數型別轉換示範\n";

$processor = new EnvironmentProcessor();

echo "環境變數都是字串，但會自動轉換為正確的型別：\n";
echo "- 'true' -> " . var_export($config['http']['verify'], true) . " (boolean)\n";
echo "- '45' -> " . var_export($config['http']['timeout'], true) . " (integer)\n";
echo "- '157286400' -> " . var_export($config['upload']['max_file_size'], true) . " (integer)\n\n";

// 4. 使用全域函數存取環境配置
echo "4. 使用全域函數存取環境配置\n";

echo "使用 file_service_config() 函數：\n";
echo "- Base URL: " . file_service_config('baseUrl') . "\n";
echo "- Cache Driver: " . file_service_config('cache.driver') . "\n";
echo "- Log Channel: " . file_service_config('logging.channel') . "\n\n";

// 5. 無效環境變數處理
echo "5. 無效環境變數處理\n";

echo "設定無效的環境變數值...\n";
$_ENV['FILE_SERVICE_TIMEOUT'] = 'invalid_number';
$_ENV['FILE_SERVICE_SSL_VERIFY'] = 'invalid_boolean';

// 重新載入配置
$configWithInvalid = GlobalConfigManager::loadConfig();

echo "無效的環境變數會被忽略，保持預設值：\n";
echo "- Timeout (無效值被忽略): " . $configWithInvalid['http']['timeout'] . "\n";
echo "- SSL Verify (無效值被忽略): " . ($configWithInvalid['http']['verify'] ? 'true' : 'false') . "\n\n";

// 6. 實際應用範例
echo "6. 實際應用範例\n";

// 恢復有效的環境變數
$_ENV['FILE_SERVICE_TIMEOUT'] = '45';
$_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';

echo "使用環境變數配置創建 FileServiceClient：\n";

try {
    $client = new FileServiceClient();
    echo "- 客戶端成功創建\n";
    echo "- 使用的 Base URL: " . $client->getBaseUrl() . "\n";
    
    // 展示如何結合使用者選項和環境變數
    $customOptions = [
        'headers' => [
            'Authorization' => 'Bearer ' . ($_ENV['API_TOKEN'] ?? 'demo-token'),
            'X-Environment' => $_ENV['APP_ENV'] ?? 'production',
        ]
    ];
    
    $clientWithOptions = new FileServiceClient($customOptions);
    echo "- 結合自定義選項的客戶端也成功創建\n";
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}

echo "\n";

// 7. .env 檔案範例
echo "7. .env 檔案範例\n";
echo "在實際專案中，您可以在 .env 檔案中設定這些變數：\n\n";

echo "```env\n";
echo "# File Service 基本配置\n";
echo "FILE_SERVICE_BASE_URL=https://api.yourservice.com\n";
echo "FILE_SERVICE_TIMEOUT=30\n";
echo "FILE_SERVICE_CONNECT_TIMEOUT=10\n";
echo "FILE_SERVICE_SSL_VERIFY=true\n";
echo "\n";
echo "# 上傳配置\n";
echo "FILE_SERVICE_UPLOAD_TIMEOUT=120\n";
echo "FILE_SERVICE_MAX_FILE_SIZE=104857600  # 100MB\n";
echo "\n";
echo "# 下載配置\n";
echo "FILE_SERVICE_DOWNLOAD_TIMEOUT=300\n";
echo "\n";
echo "# 快取配置\n";
echo "FILE_SERVICE_CACHE_ENABLED=true\n";
echo "FILE_SERVICE_CACHE_DRIVER=redis\n";
echo "FILE_SERVICE_CACHE_TTL=3600\n";
echo "\n";
echo "# 日誌配置\n";
echo "FILE_SERVICE_LOG_ENABLED=true\n";
echo "FILE_SERVICE_LOG_LEVEL=info\n";
echo "FILE_SERVICE_LOG_CHANNEL=file-service\n";
echo "\n";
echo "# 重試配置\n";
echo "FILE_SERVICE_RETRY_MAX_ATTEMPTS=3\n";
echo "FILE_SERVICE_RETRY_DELAY=1000\n";
echo "FILE_SERVICE_RETRY_MULTIPLIER=2\n";
echo "```\n\n";

// 8. 不同環境的配置策略
echo "8. 不同環境的配置策略\n";

echo "建議的環境配置策略：\n\n";

echo "開發環境 (.env.local)：\n";
echo "- FILE_SERVICE_BASE_URL=http://localhost:8080\n";
echo "- FILE_SERVICE_LOG_LEVEL=debug\n";
echo "- FILE_SERVICE_SSL_VERIFY=false\n\n";

echo "測試環境 (.env.testing)：\n";
echo "- FILE_SERVICE_BASE_URL=https://test-api.yourservice.com\n";
echo "- FILE_SERVICE_LOG_LEVEL=warning\n";
echo "- FILE_SERVICE_TIMEOUT=10  # 更短的超時用於快速測試\n\n";

echo "生產環境 (.env.production)：\n";
echo "- FILE_SERVICE_BASE_URL=https://api.yourservice.com\n";
echo "- FILE_SERVICE_LOG_LEVEL=error\n";
echo "- FILE_SERVICE_SSL_VERIFY=true\n";
echo "- FILE_SERVICE_CACHE_ENABLED=true\n\n";

// 9. 環境變數驗證
echo "9. 環境變數驗證\n";

$requiredEnvVars = ['FILE_SERVICE_BASE_URL'];
$missingVars = [];

foreach ($requiredEnvVars as $var) {
    if (empty($_ENV[$var])) {
        $missingVars[] = $var;
    }
}

if (empty($missingVars)) {
    echo "✓ 所有必要的環境變數都已設定\n";
} else {
    echo "✗ 缺少必要的環境變數: " . implode(', ', $missingVars) . "\n";
}

echo "\n";

// 10. 清理
echo "10. 清理環境變數\n";
echo "清理測試設定的環境變數...\n";

foreach (array_keys($envVars) as $key) {
    unset($_ENV[$key]);
}

echo "環境變數已清理完成。\n\n";

echo "=== 環境變數配置範例結束 ===\n";
