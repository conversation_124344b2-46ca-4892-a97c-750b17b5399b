<?php

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\GlobalConfigManager;

// 載入全域配置函數
require_once __DIR__ . '/../src/helpers.php';

echo "=== Global Config Manager 使用範例 ===\n\n";

// 1. 環境偵測
echo "1. 環境偵測:\n";
echo "是否為 Laravel 環境: " . (GlobalConfigManager::isLaravel() ? 'Yes' : 'No') . "\n\n";

// 2. 基本配置存取
echo "2. 基本配置存取:\n";
echo "Base URL: " . GlobalConfigManager::config('baseUrl') . "\n";
echo "HTTP Timeout: " . GlobalConfigManager::config('http.timeout') . "\n";
echo "不存在的配置 (預設值): " . GlobalConfigManager::config('non.existent', 'default_value') . "\n\n";

// 3. 使用全域函數
echo "3. 使用全域函數:\n";
echo "Base URL (使用 file_service_config): " . file_service_config('baseUrl') . "\n";
echo "Upload timeout: " . file_service_config('upload.timeout') . "\n\n";

// 4. 動態配置修改
echo "4. 動態配置修改:\n";
echo "修改前的 HTTP timeout: " . GlobalConfigManager::config('http.timeout') . "\n";

GlobalConfigManager::set('http.timeout', 999);
echo "修改後的 HTTP timeout: " . GlobalConfigManager::config('http.timeout') . "\n";

// 使用陣列批量設定
GlobalConfigManager::set([
    'custom.setting1' => 'value1',
    'custom.setting2' => 'value2',
    'http.custom_header' => 'Custom-Value'
]);

echo "自訂設定1: " . GlobalConfigManager::config('custom.setting1') . "\n";
echo "自訂設定2: " . GlobalConfigManager::config('custom.setting2') . "\n";
echo "自訂 HTTP 標頭: " . GlobalConfigManager::config('http.custom_header') . "\n\n";

// 5. 配置移除
echo "5. 配置移除:\n";
echo "移除前的自訂設定1: " . GlobalConfigManager::config('custom.setting1', 'not found') . "\n";

GlobalConfigManager::forget('custom.setting1');
echo "移除後的自訂設定1: " . GlobalConfigManager::config('custom.setting1', 'not found') . "\n\n";

// 6. 獲取完整配置區塊
echo "6. 獲取完整配置區塊:\n";
$httpConfig = GlobalConfigManager::config('http');
echo "HTTP 配置:\n";
foreach ($httpConfig as $key => $value) {
    echo "  {$key}: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
}
echo "\n";

// 7. 環境變數處理示範
echo "7. 環境變數處理:\n";
echo "設定環境變數...\n";
$_ENV['FILE_SERVICE_BASE_URL'] = 'https://api.example.com';
$_ENV['FILE_SERVICE_TIMEOUT'] = '45';
$_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';

// 重新載入配置以應用環境變數
echo "重新載入後的配置:\n";
$config = GlobalConfigManager::loadConfig();
echo "Base URL (來自環境變數): " . $config['baseUrl'] . "\n";
echo "Timeout (來自環境變數): " . $config['http']['timeout'] . "\n";
echo "SSL Verify (來自環境變數): " . ($config['http']['verify'] ? 'true' : 'false') . "\n\n";

// 8. 清除運行時配置
echo "8. 清除運行時配置:\n";
echo "清除前的 HTTP timeout: " . GlobalConfigManager::config('http.timeout') . "\n";

GlobalConfigManager::clearRuntimeConfig();
echo "清除後的 HTTP timeout: " . GlobalConfigManager::config('http.timeout') . "\n\n";

echo "=== 範例完成 ===\n";