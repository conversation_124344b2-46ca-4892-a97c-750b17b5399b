# 配置發布功能使用指南

## 概述

配置發布功能允許您將 File Service Client 的預設配置檔案複製到您的專案中，以便進行自訂配置。

## 功能特點

- ✅ 支援 Laravel 和原生 PHP 專案
- ✅ 自動環境偵測
- ✅ 配置檔案覆蓋確認機制
- ✅ 配置目錄自動建立
- ✅ 發布狀態檢查
- ✅ 命令列工具支援

## 使用方法

### Laravel 專案

#### 方法 1：使用 Artisan 命令（推薦）

```bash
# 發布配置檔案
php artisan file-service:publish-config

# 強制覆蓋現有檔案
php artisan file-service:publish-config --force

# 指定自訂目標路徑
php artisan file-service:publish-config --target=/path/to/config/file-service.php
```

#### 方法 2：使用 Laravel 標準發布方式

```bash
php artisan vendor:publish --tag=file-service-config
```

#### 方法 3：程式化發布

```php
use FDMC\FileServiceClient\ConfigPublisher;

$publisher = app(ConfigPublisher::class);

// 發布到預設位置
$success = $publisher->publishToLaravel();

// 強制覆蓋
$success = $publisher->publishToLaravel(null, true);

// 檢查發布狀態
$status = $publisher->getPublishStatus();
```

### 原生 PHP 專案

#### 方法 1：使用命令列工具（推薦）

```bash
# 發布配置檔案
php bin/publish-config.php

# 強制覆蓋現有檔案
php bin/publish-config.php --force

# 指定自訂目標路徑
php bin/publish-config.php --target /path/to/config/file-service.php

# 顯示幫助資訊
php bin/publish-config.php --help
```

#### 方法 2：程式化發布

```php
use FDMC\FileServiceClient\ConfigPublisher;
use FDMC\FileServiceClient\ConfigLogger;

$logger = ConfigLogger::getInstance();
$publisher = new ConfigPublisher($logger);

// 發布到預設位置
$success = $publisher->publishToNative();

// 發布到自訂位置
$success = $publisher->publishToNative('/path/to/config/file-service.php');

// 強制覆蓋
$success = $publisher->publishToNative(null, true);

// 檢查發布狀態
$status = $publisher->getPublishStatus();
```

## 配置檔案位置

### Laravel 專案
- 預設位置：`config/file-service.php`
- 使用 Laravel 的 `config_path()` 函數確定路徑

### 原生 PHP 專案
- 預設位置：`{專案根目錄}/config/file-service.php`
- 自動偵測專案根目錄（基於 `composer.json` 檔案）

## 發布狀態檢查

您可以檢查配置檔案的發布狀態：

```php
$publisher = new ConfigPublisher();
$status = $publisher->getPublishStatus();

echo "已發布：" . ($status['published'] ? '是' : '否') . "\n";
echo "目標路徑：" . $status['target_path'] . "\n";
echo "環境類型：" . $status['environment'] . "\n";

if ($status['published']) {
    echo "檔案大小：" . $status['file_size'] . " 位元組\n";
    echo "修改時間：" . date('Y-m-d H:i:s', $status['modified_time']) . "\n";
    echo "可讀：" . ($status['readable'] ? '是' : '否') . "\n";
    echo "可寫：" . ($status['writable'] ? '是' : '否') . "\n";
}
```

## 覆蓋確認機制

當目標位置已存在配置檔案時：

1. **互動環境**：會提示使用者確認是否覆蓋
2. **非互動環境**：預設不覆蓋，除非使用 `--force` 參數
3. **程式化調用**：可以設定 `$force` 參數控制行為

## 錯誤處理

配置發布功能提供完善的錯誤處理：

```php
try {
    $success = $publisher->publishToNative();
    if ($success) {
        echo "發布成功！\n";
    } else {
        echo "發布被取消或失敗。\n";
    }
} catch (ConfigurationException $e) {
    echo "發布失敗：" . $e->getMessage() . "\n";
    
    // 顯示修復建議
    foreach ($e->getRepairSuggestions() as $suggestion) {
        echo "• " . $suggestion . "\n";
    }
}
```

## 自訂輸入處理（測試用）

在測試環境中，您可以自訂輸入處理器：

```php
$publisher = new ConfigPublisher();

// 設定自動確認覆蓋
$publisher->setInputHandler(function($filePath) {
    return true; // 總是覆蓋
});

// 設定自動拒絕覆蓋
$publisher->setInputHandler(function($filePath) {
    return false; // 從不覆蓋
});
```

## 範例

完整的使用範例請參考：
- `examples/config_publish_example.php` - 配置發布功能展示
- `bin/publish-config.php` - 命令列工具
- `tests/ConfigPublisherTest.php` - 單元測試範例

## 注意事項

1. **權限**：確保目標目錄有寫入權限
2. **備份**：覆蓋前建議備份現有配置檔案
3. **環境變數**：發布後可以使用環境變數覆蓋配置值
4. **版本控制**：建議將發布的配置檔案加入版本控制

## 疑難排解

### 常見問題

1. **權限不足**
   ```
   錯誤：無法建立配置目錄
   解決：檢查目標目錄的寫入權限
   ```

2. **找不到專案根目錄**
   ```
   錯誤：未找到 composer.json
   解決：確保在包含 composer.json 的專案中執行
   ```

3. **配置檔案格式錯誤**
   ```
   錯誤：配置檔案格式錯誤
   解決：檢查 PHP 語法，確保返回陣列
   ```

### 除錯模式

設定環境變數啟用除錯模式：

```bash
export DEBUG=true
php bin/publish-config.php
```

這將顯示詳細的錯誤資訊和堆疊追蹤。