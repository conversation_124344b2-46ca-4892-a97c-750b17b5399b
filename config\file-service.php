<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | File Service 基礎配置
    |--------------------------------------------------------------------------
    |
    | 這些設定控制 File Service Client 的基本行為
    |
    */
    
    'baseUrl' => env('FILE_SERVICE_BASE_URL', 'http://localhost:8080'),
    
    /*
    |--------------------------------------------------------------------------
    | HTTP 客戶端配置
    |--------------------------------------------------------------------------
    |
    | HTTP 請求的相關設定
    |
    */
    
    'http' => [
        'timeout' => is_numeric(env('FILE_SERVICE_TIMEOUT')) ? (int) env('FILE_SERVICE_TIMEOUT') : 30,
        'connect_timeout' => is_numeric(env('FILE_SERVICE_CONNECT_TIMEOUT')) ? (int) env('FILE_SERVICE_CONNECT_TIMEOUT') : 10,
        'read_timeout' => is_numeric(env('FILE_SERVICE_READ_TIMEOUT')) ? (int) env('FILE_SERVICE_READ_TIMEOUT') : 60,
        'verify' => filter_var(env('FILE_SERVICE_SSL_VERIFY', false), FILTER_VALIDATE_BOOLEAN),
        'http_errors' => false,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | HTTP 標頭配置
    |--------------------------------------------------------------------------
    |
    | 預設的 HTTP 請求標頭
    |
    */
    
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'User-Agent' => 'FDMC-FileServiceClient/1.0',
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 檔案上傳配置
    |--------------------------------------------------------------------------
    |
    | 檔案上傳相關的設定
    |
    */
    
    'upload' => [
        'timeout' => is_numeric(env('FILE_SERVICE_UPLOAD_TIMEOUT')) ? (int) env('FILE_SERVICE_UPLOAD_TIMEOUT') : 120,
        'max_file_size' => is_numeric(env('FILE_SERVICE_MAX_FILE_SIZE')) ? (int) env('FILE_SERVICE_MAX_FILE_SIZE') : 104857600, // 100MB
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'text/plain',
            'application/json',
            'application/xml',
            'application/zip',
            'application/x-zip-compressed',
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 檔案下載配置
    |--------------------------------------------------------------------------
    |
    | 檔案下載相關的設定
    |
    */
    
    'download' => [
        'timeout' => is_numeric(env('FILE_SERVICE_DOWNLOAD_TIMEOUT')) ? (int) env('FILE_SERVICE_DOWNLOAD_TIMEOUT') : 300,
        'stream_buffer_size' => is_numeric(env('FILE_SERVICE_STREAM_BUFFER_SIZE')) ? (int) env('FILE_SERVICE_STREAM_BUFFER_SIZE') : 8192,
        'large_file_threshold' => is_numeric(env('FILE_SERVICE_LARGE_FILE_THRESHOLD')) ? (int) env('FILE_SERVICE_LARGE_FILE_THRESHOLD') : 52428800, // 50MB
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 快取配置
    |--------------------------------------------------------------------------
    |
    | 配置快取相關設定
    |
    */
    
    'cache' => [
        'enabled' => filter_var(env('FILE_SERVICE_CACHE_ENABLED', false), FILTER_VALIDATE_BOOLEAN),
        'driver' => env('FILE_SERVICE_CACHE_DRIVER', 'file'),
        'ttl' => is_numeric(env('FILE_SERVICE_CACHE_TTL')) ? (int) env('FILE_SERVICE_CACHE_TTL') : 3600,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 日誌配置
    |--------------------------------------------------------------------------
    |
    | 日誌記錄相關設定
    |
    */
    
    'logging' => [
        'enabled' => filter_var(env('FILE_SERVICE_LOG_ENABLED', true), FILTER_VALIDATE_BOOLEAN),
        'level' => env('FILE_SERVICE_LOG_LEVEL', 'info'),
        'channel' => env('FILE_SERVICE_LOG_CHANNEL', 'default'),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 重試機制配置
    |--------------------------------------------------------------------------
    |
    | API 請求失敗時的重試設定
    |
    */
    
    'retry' => [
        'max_attempts' => is_numeric(env('FILE_SERVICE_RETRY_MAX_ATTEMPTS')) ? (int) env('FILE_SERVICE_RETRY_MAX_ATTEMPTS') : 3,
        'delay' => is_numeric(env('FILE_SERVICE_RETRY_DELAY')) ? (int) env('FILE_SERVICE_RETRY_DELAY') : 1000, // 毫秒
        'multiplier' => is_numeric(env('FILE_SERVICE_RETRY_MULTIPLIER')) ? (float) env('FILE_SERVICE_RETRY_MULTIPLIER') : 2.0,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 全域函數配置
    |--------------------------------------------------------------------------
    |
    | 控制是否註冊全域 config 函數
    |
    */
    
    'enable_global_config_function' => filter_var(
        env('FILE_SERVICE_ENABLE_GLOBAL_CONFIG_FUNCTION', false), 
        FILTER_VALIDATE_BOOLEAN
    ),
];