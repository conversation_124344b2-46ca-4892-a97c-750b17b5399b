<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\Logging\ConfigLogger;
use FDMC\FileServiceClient\Exceptions\ConfigurationException;
use PHPUnit\Framework\TestCase;

/**
 * 配置日誌記錄器測試
 */
class ConfigLoggerTest extends TestCase
{
    private ConfigLogger $logger;
    private array $loggedRecords = [];

    protected function setUp(): void
    {
        $this->logger = new ConfigLogger(ConfigLogger::LEVEL_DEBUG);
        $this->logger->clearHandlers();
        $this->loggedRecords = [];

        // 添加測試處理器
        $this->logger->addHandler(function (array $record): void {
            $this->loggedRecords[] = $record;
        });
    }

    public function testSingletonInstance(): void
    {
        $instance1 = ConfigLogger::getInstance();
        $instance2 = ConfigLogger::getInstance();

        $this->assertSame($instance1, $instance2);
        $this->assertInstanceOf(ConfigLogger::class, $instance1);
    }

    public function testSetMinLevel(): void
    {
        $this->logger->setMinLevel(ConfigLogger::LEVEL_WARNING);

        // 低於最小級別的日誌不應被記錄
        $this->logger->debug('Debug message');
        $this->logger->info('Info message');
        $this->assertEmpty($this->loggedRecords);

        // 高於或等於最小級別的日誌應被記錄
        $this->logger->warning('Warning message');
        $this->assertCount(1, $this->loggedRecords);
    }

    public function testLogLevels(): void
    {
        $this->logger->debug('Debug message');
        $this->logger->info('Info message');
        $this->logger->warning('Warning message');
        $this->logger->error('Error message');
        $this->logger->critical('Critical message');

        $this->assertCount(5, $this->loggedRecords);

        $expectedLevels = [
            ConfigLogger::LEVEL_DEBUG,
            ConfigLogger::LEVEL_INFO,
            ConfigLogger::LEVEL_WARNING,
            ConfigLogger::LEVEL_ERROR,
            ConfigLogger::LEVEL_CRITICAL,
        ];

        foreach ($expectedLevels as $index => $expectedLevel) {
            $this->assertEquals($expectedLevel, $this->loggedRecords[$index]['level']);
        }
    }

    public function testLogWithContext(): void
    {
        $context = ['key' => 'value', 'number' => 123];
        $this->logger->info('Test message', $context);

        $this->assertCount(1, $this->loggedRecords);
        $this->assertEquals($context, $this->loggedRecords[0]['context']);
    }

    public function testLogException(): void
    {
        $exception = new ConfigurationException('Test exception', 1001);
        $this->logger->logException($exception, ConfigLogger::LEVEL_ERROR, ['extra' => 'data']);

        $this->assertCount(1, $this->loggedRecords);
        $record = $this->loggedRecords[0];

        $this->assertEquals(ConfigLogger::LEVEL_ERROR, $record['level']);
        $this->assertEquals('Test exception', $record['message']);
        $this->assertEquals($exception, $record['context']['exception']);
        $this->assertEquals(ConfigurationException::class, $record['context']['exception_class']);
        $this->assertEquals(1001, $record['context']['exception_code']);
        $this->assertEquals('data', $record['context']['extra']);
    }

    public function testLogConfigLoad(): void
    {
        $source = '/path/to/config.php';
        $context = ['loaded_keys' => 5];
        $this->logger->logConfigLoad($source, $context);

        $this->assertCount(1, $this->loggedRecords);
        $record = $this->loggedRecords[0];

        $this->assertEquals(ConfigLogger::LEVEL_INFO, $record['level']);
        $this->assertStringContainsString('配置已載入', $record['message']);
        $this->assertEquals($source, $record['context']['source']);
        $this->assertEquals(5, $record['context']['loaded_keys']);
    }

    public function testLogConfigValidation(): void
    {
        // 測試成功驗證
        $this->logger->logConfigValidation(true, ['validated_keys' => 3]);

        $this->assertCount(1, $this->loggedRecords);
        $this->assertEquals(ConfigLogger::LEVEL_INFO, $this->loggedRecords[0]['level']);
        $this->assertStringContainsString('驗證成功', $this->loggedRecords[0]['message']);

        // 清除記錄
        $this->loggedRecords = [];

        // 測試失敗驗證
        $this->logger->logConfigValidation(false, ['errors' => 2]);

        $this->assertCount(1, $this->loggedRecords);
        $this->assertEquals(ConfigLogger::LEVEL_WARNING, $this->loggedRecords[0]['level']);
        $this->assertStringContainsString('驗證失敗', $this->loggedRecords[0]['message']);
    }

    public function testLogTypeConversion(): void
    {
        $key = 'timeout';
        $originalValue = '30';
        $convertedValue = 30;
        $type = 'integer';

        $this->logger->logTypeConversion($key, $originalValue, $convertedValue, $type);

        $this->assertCount(1, $this->loggedRecords);
        $record = $this->loggedRecords[0];

        $this->assertEquals(ConfigLogger::LEVEL_DEBUG, $record['level']);
        $this->assertStringContainsString('型別轉換成功', $record['message']);
        $this->assertEquals($key, $record['context']['key']);
        $this->assertEquals($originalValue, $record['context']['original_value']);
        $this->assertEquals($convertedValue, $record['context']['converted_value']);
        $this->assertEquals($type, $record['context']['target_type']);
    }

    public function testLogTypeConversionFailure(): void
    {
        $key = 'enabled';
        $value = 'maybe';
        $type = 'boolean';
        $reason = '無效的布林值';

        $this->logger->logTypeConversionFailure($key, $value, $type, $reason);

        $this->assertCount(1, $this->loggedRecords);
        $record = $this->loggedRecords[0];

        $this->assertEquals(ConfigLogger::LEVEL_WARNING, $record['level']);
        $this->assertStringContainsString('型別轉換失敗', $record['message']);
        $this->assertEquals($key, $record['context']['key']);
        $this->assertEquals($value, $record['context']['value']);
        $this->assertEquals($type, $record['context']['target_type']);
        $this->assertEquals($reason, $record['context']['reason']);
    }

    public function testAddHandler(): void
    {
        $handlerCalled = false;

        $this->logger->addHandler(function (array $record) use (&$handlerCalled): void {
            $handlerCalled = true;
        });

        $this->logger->info('Test message');

        $this->assertTrue($handlerCalled);
        $this->assertCount(1, $this->loggedRecords); // 原有處理器也應該被調用
    }

    public function testClearHandlers(): void
    {
        $this->logger->clearHandlers();
        $this->logger->info('Test message');

        $this->assertEmpty($this->loggedRecords);
    }

    public function testLogRecord(): void
    {
        $this->logger->info('Test message', ['key' => 'value']);

        $this->assertCount(1, $this->loggedRecords);
        $record = $this->loggedRecords[0];

        $this->assertEquals(ConfigLogger::LEVEL_INFO, $record['level']);
        $this->assertEquals('INFO', $record['level_name']);
        $this->assertEquals('Test message', $record['message']);
        $this->assertEquals(['key' => 'value'], $record['context']);
        $this->assertInstanceOf(\DateTimeImmutable::class, $record['datetime']);
        $this->assertEquals('file-service-config', $record['channel']);
    }

    public function testGetLevelName(): void
    {
        $this->assertEquals('DEBUG', ConfigLogger::getLevelName(ConfigLogger::LEVEL_DEBUG));
        $this->assertEquals('INFO', ConfigLogger::getLevelName(ConfigLogger::LEVEL_INFO));
        $this->assertEquals('WARNING', ConfigLogger::getLevelName(ConfigLogger::LEVEL_WARNING));
        $this->assertEquals('ERROR', ConfigLogger::getLevelName(ConfigLogger::LEVEL_ERROR));
        $this->assertEquals('CRITICAL', ConfigLogger::getLevelName(ConfigLogger::LEVEL_CRITICAL));
        $this->assertEquals('UNKNOWN', ConfigLogger::getLevelName(9999));
    }

    public function testGetLevelFromName(): void
    {
        $this->assertEquals(ConfigLogger::LEVEL_DEBUG, ConfigLogger::getLevelFromName('DEBUG'));
        $this->assertEquals(ConfigLogger::LEVEL_INFO, ConfigLogger::getLevelFromName('INFO'));
        $this->assertEquals(ConfigLogger::LEVEL_WARNING, ConfigLogger::getLevelFromName('WARNING'));
        $this->assertEquals(ConfigLogger::LEVEL_ERROR, ConfigLogger::getLevelFromName('ERROR'));
        $this->assertEquals(ConfigLogger::LEVEL_CRITICAL, ConfigLogger::getLevelFromName('CRITICAL'));

        // 測試大小寫不敏感
        $this->assertEquals(ConfigLogger::LEVEL_DEBUG, ConfigLogger::getLevelFromName('debug'));
        $this->assertEquals(ConfigLogger::LEVEL_INFO, ConfigLogger::getLevelFromName('info'));

        // 測試未知級別
        $this->assertEquals(ConfigLogger::LEVEL_INFO, ConfigLogger::getLevelFromName('UNKNOWN'));
    }

    public function testMinLevelFiltering(): void
    {
        $this->logger->setMinLevel(ConfigLogger::LEVEL_ERROR);

        $this->logger->debug('Debug message');
        $this->logger->info('Info message');
        $this->logger->warning('Warning message');
        $this->logger->error('Error message');
        $this->logger->critical('Critical message');

        // 只有 ERROR 和 CRITICAL 級別的日誌應該被記錄
        $this->assertCount(2, $this->loggedRecords);
        $this->assertEquals(ConfigLogger::LEVEL_ERROR, $this->loggedRecords[0]['level']);
        $this->assertEquals(ConfigLogger::LEVEL_CRITICAL, $this->loggedRecords[1]['level']);
    }

    public function testMultipleHandlers(): void
    {
        $handler1Called = false;
        $handler2Called = false;

        $this->logger->addHandler(function () use (&$handler1Called): void {
            $handler1Called = true;
        });

        $this->logger->addHandler(function () use (&$handler2Called): void {
            $handler2Called = true;
        });

        $this->logger->info('Test message');

        $this->assertTrue($handler1Called);
        $this->assertTrue($handler2Called);
        $this->assertCount(1, $this->loggedRecords); // 原有測試處理器
    }
}