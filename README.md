# File Service Client

[![PHP Version](https://img.shields.io/badge/php-%3E%3D8.0-brightgreen.svg?style=flat-square)](https://php.net/)

具有版本控制功能的檔案管理系統 PHP 客戶端套件。採用現代化的架構設計，提供簡潔易用的 API 介面。

## ✨ 特性

- 🎯 **簡潔的 API** - 統一的入口點，易於學習和使用
- 🏗️ **模組化架構** - HTTP 層與業務邏輯分離，便於測試和維護
- 🧪 **高可測試性** - 支援依賴注入，便於單元測試
- 🔄 **版本控制** - 完整的檔案版本管理功能
- 🔍 **搜尋功能** - 強大的檔案和資料夾搜尋能力
- ⚡ **效能優化** - 基於 Guzzle HTTP 的高效網路通訊
- 🛡️ **錯誤處理** - 完善的例外處理機制
- 📤 **多種上傳方式** - 支援檔案路徑、內容字串、串流上傳
- 💾 **多儲存後端** - 支援 S3、FTP、NAS、本地檔案系統

## 📦 安裝

```bash
composer require fdmc/file-service-client
```

或手動安裝依賴：

```bash
composer install
```

## 🚀 快速開始

### 基本使用

```php
<?php

require_once 'vendor/autoload.php';

use FDMC\FileServiceClient\FileServiceClient;
use FDMC\FileServiceClient\FileServiceException;

// 初始化客戶端
$client = new FileServiceClient([
    'base_uri' => 'https://your-file-service-url',
    'timeout' => 60,
    'verify' => true
]);


// 基本參數
$companyId = 1;
$scope = 'documents';
$userId = 123;

try {
    // 列出根目錄下的所有物件
    $objects = $client->listRootObjects($companyId, $scope, $userId);
    echo "找到 " . count($objects['data']) . " 個物件\n";
    
    // 建立新資料夾
    $folderData = $client->buildObjectData(
        company_id: $companyId,
        scope: $scope,
        name: '我的文件',
        type: 'folder'
    );
    
    $folder = $client->createObject($userId, $folderData);
    echo "已建立資料夾: " . $folder['data']['id'] . "\n";
    
} catch (FileServiceException $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}
```

### 進階配置

```php
// 使用自訂配置初始化
$client = new FileServiceClient([
    'base_uri' => 'https://api.example.com',
    'timeout' => 60,           // 請求超時時間（秒）
    'verify' => true,          // SSL 證書驗證
    'connect_timeout' => 10,   // 連接超時時間（秒）
    'headers' => [
        'User-Agent' => 'MyApp/1.0',
        'X-API-Version' => 'v1'
    ]
]);

// 或使用依賴注入（用於測試）
use FDMC\FileServiceClient\Http\HttpClientInterface;

$customHttpClient = new CustomHttpClient();
$client = new FileServiceClient([], $customHttpClient);
```

## 🏗️ 架構設計

### 目錄結構

```
src/
├── FileServiceClient.php          # 📦 主要 API 入口點
├── FileServiceException.php       # ⚠️  例外處理
├── StorageManager.php             # 💾 儲存管理（獨立模組）
└── Http/                          # 🌐 HTTP 傳輸層（內部）
    ├── HttpClientInterface.php    #    HTTP 客戶端介面
    └── GuzzleHttpClient.php       #    Guzzle HTTP 實現

examples/
├── http_client_usage.php          # 📋 基本使用示例
└── architecture_comparison.php    # 🔍 架構設計比較
```

### 設計原則

1. **📦 封裝性** - HTTP 細節對使用者隱藏，提供簡潔的 API
2. **🧪 可測試性** - 支援依賴注入，便於進行單元測試
3. **🔧 可維護性** - 模組化設計，各層職責清晰
4. **🚀 易用性** - 統一的入口點，合理的預設配置
5. **🔍 可擴展性** - 基於介面的設計，便於新增功能

## 📚 主要功能

### 🗂️ 物件操作

| 方法                  | 參數                                                         | 描述             | 用途             |
| --------------------- | ------------------------------------------------------------ | ---------------- | ---------------- |
| `listRootObjects()`   | `(int $companyId, string $scope, int $userId)`              | 列出根目錄物件   | 獲取根目錄內容   |
| `listObjects()`       | `(int $userId, string $objectId)`                           | 列出指定物件內容 | 獲取資料夾內容   |
| `createObject()`      | `(int $userId, array $objectData)`                          | 創建新物件       | 建立檔案或資料夾 |
| `deleteObject()`      | `(string $objectId, int $userId)`                           | 刪除物件         | 移除檔案或資料夾 |
| `getObjectInfo()`     | `(string $objectId, int $userId)`                           | 獲取物件資訊     | 查看檔案詳情     |
| `checkObjectExists()` | `(string $objectId, int $userId, ?string $versionId = null)` | 檢查物件存在性   | 驗證檔案是否存在 |
| `downloadObject()`    | `(string $objectId, int $userId)`                           | 下載檔案         | 獲取檔案內容     |
| `renameObject()`      | `(string $objectId, int $userId, string $newName)`          | 重命名物件       | 修改檔案名稱     |
| `copyObjects()`       | `(string $objectId, int $userId, string $targetId, ?string $newName = null, ?string $duplicateStrategy = null)` | 複製物件 | 複製檔案或資料夾 |
| `moveObjects()`       | `(string $objectId, int $userId, string $targetId, ?string $duplicateStrategy = null)` | 移動物件 | 移動檔案或資料夾 |
| `searchObjects()`     | `(int $companyId, string $scope, int $userId, string $query)` | 搜尋物件        | 搜尋檔案和資料夾 |

### 📤 檔案上傳

| 方法                      | 參數                                                         | 描述             | 用途             |
| ------------------------- | ------------------------------------------------------------ | ---------------- | ---------------- |
| `uploadFile()`            | `(int $userId, string $filePath, string $objectId, ?callable $progressCallback = null)` | 從檔案路徑上傳   | 上傳本地檔案     |
| `uploadFileFromContent()` | `(int $userId, string $fileContent, string $fileName, string $objectId, ?callable $progressCallback = null)` | 從內容字串上傳   | 上傳文字或資料   |
| `uploadFileFromStream()`  | `(int $userId, $stream, string $fileName, string $objectId, ?callable $progressCallback = null)` | 從串流上傳       | 上傳大型檔案     |

### 📝 版本管理

| 方法                       | 參數                                                    | 描述             | 用途             |
| -------------------------- | ------------------------------------------------------- | ---------------- | ---------------- |
| `getVersionList()`         | `(string $objectId, int $userId)`                      | 獲取版本列表     | 查看檔案版本歷史 |
| `getObjectVersionInfo()`   | `(string $objectId, string $versionId, int $userId)`   | 獲取版本資訊     | 查看特定版本詳情 |
| `downloadObjectVersion()`  | `(string $objectId, string $versionId, int $userId)`   | 下載特定版本     | 獲取歷史版本內容 |
| `restoreVersion()`         | `(string $objectId, string $versionId, int $userId)`   | 還原到特定版本   | 恢復檔案到舊版本 |



### 🔍 搜尋功能

| 方法              | 參數                                                         | 描述             | 用途             |
| ----------------- | ------------------------------------------------------------ | ---------------- | ---------------- |
| `searchObjects()` | `(int $companyId, string $scope, int $userId, string $query)` | 搜尋檔案和資料夾 | 根據關鍵字搜尋物件 |



### 💾 儲存管理

儲存管理功能已獨立為 `StorageManager` 類別：

| 方法               | 參數                                                         | 描述             | 用途             |
| ------------------ | ------------------------------------------------------------ | ---------------- | ---------------- |
| `listStorages()`   | `(int $userId)`                                              | 列出所有儲存     | 獲取儲存列表     |
| `createStorage()`  | `(int $userId, array $storageData)`                         | 創建新儲存       | 建立儲存配置     |
| `getStorage()`     | `(string $storageId, int $userId)`                          | 獲取儲存詳情     | 查看儲存資訊     |
| `updateStorage()`  | `(string $storageId, int $userId, array $storageData)`      | 更新儲存設定     | 修改儲存配置     |
| `deleteStorage()`  | `(string $storageId, int $userId)`                          | 刪除儲存         | 移除儲存配置     |
| `buildStorageData()` | `(string $name, string $type, ?string $host = null, ?string $remark = null, ?array $payload = null)` | 建立儲存資料 | 構建儲存配置陣列 |


## 🛠️ 輔助方法

### 建立物件資料

```php
$objectData = $client->buildObjectData(
    company_id: 1,
    scope: 'documents',
    name: 'important-document.pdf',
    type: 'file',
    parent: 'folder-id',           // 可選：父資料夾 ID
    storage_path: '/path/to/file', // 可選：儲存路徑
    mimeType: 'application/pdf',   // 可選：MIME 類型
    size: 2048,                    // 可選：檔案大小
    sha1: 'file-hash',             // 可選：SHA1 雜湊
    md5: 'file-md5'                // 可選：MD5 雜湊
);
```

### 建立儲存資料

```php
$storageData = $storageManager->buildStorageData(
    name: 'AWS S3 Storage',
    type: 'S3',
    host: 's3.amazonaws.com',
    remark: '生產環境 S3 儲存',
    payload: [
        'bucket' => 'production-files',
        'region' => 'ap-northeast-1',
        'access_key' => 'your-access-key',
        'secret_key' => 'your-secret-key'
    ]
);
```


## 🎯 支援的儲存類型

| 類型       | 說明               | 配置範例                         |
| ---------- | ------------------ | -------------------------------- |
| `S3`       | Amazon S3 相容儲存 | `bucket`, `region`, `access_key` |
| `FTP`      | FTP 伺服器         | `username`, `password`, `path`   |
| `NAS`      | 網路附加儲存       | `share_path`, `credentials`      |
| `FS_LOCAL` | 本地檔案系統       | `path`                           |
| `AP_LOCAL` | 應用程式本地儲存   | `path`, `permissions`            |

## 📤 檔案上傳

檔案上傳採用兩步驟流程：
1. **先創建物件** - 使用 `createObject()` 建立檔案物件
2. **再上傳內容** - 使用上傳方法將檔案內容上傳到該物件

### 基本上傳流程

```php
// 步驟 1: 創建檔案物件
$objectData = $client->buildObjectData(
    company_id: 1,
    scope: 'uploads',
    name: 'document.pdf',
    type: 'file',
    mimeType: 'application/pdf'
);

$object = $client->createObject($userId, $objectData);
$objectId = $object['data']['id'];

// 步驟 2: 上傳檔案內容到該物件
$result = $client->uploadFile($userId, '/path/to/document.pdf', $objectId);
echo "✅ 檔案已上傳完成: " . $objectId . "\n";
```

### 從內容上傳

```php
// 先創建物件
$objectData = $client->buildObjectData(
    company_id: 1,
    scope: 'data',
    name: 'message.json',
    type: 'file',
    mimeType: 'application/json'
);

$object = $client->createObject($userId, $objectData);
$objectId = $object['data']['id'];

// 再上傳內容
$fileContent = json_encode(['message' => 'Hello World!']);
$result = $client->uploadFileFromContent($userId, $fileContent, 'message.json', $objectId);
```

### 帶進度監控的上傳

```php
// 創建物件
$object = $client->createObject($userId, $objectData);
$objectId = $object['data']['id'];

// 定義進度回調
$progressCallback = function($downloadTotal, $downloadedBytes, $uploadTotal, $uploadedBytes) {
    if ($uploadTotal > 0) {
        $percentage = round(($uploadedBytes / $uploadTotal) * 100, 2);
        echo "上傳進度: {$percentage}% ({$uploadedBytes}/{$uploadTotal} bytes)\r";
    }
};

// 帶進度監控的上傳
$result = $client->uploadFile($userId, $filePath, $objectId, $progressCallback);
```

### 從串流上傳

```php
// 創建物件
$objectData = $client->buildObjectData(
    company_id: 1,
    scope: 'uploads',
    name: 'large_file.zip',
    type: 'file',
    mimeType: 'application/zip'
);

$object = $client->createObject($userId, $objectData);
$objectId = $object['data']['id'];

// 從串流上傳
$stream = fopen('/path/to/large/file.zip', 'r');
$result = $client->uploadFileFromStream($userId, $stream, 'large_file.zip', $objectId);
fclose($stream);
```

### 完整的上傳範例

```php
try {
    // 1. 建立檔案物件
    $objectData = $client->buildObjectData(
        company_id: 1,
        scope: 'documents',
        name: '重要文件.pdf',
        type: 'file',
        parent: $folderId, // 可選：指定父資料夾
        mimeType: 'application/pdf'
    );
    
    $object = $client->createObject($userId, $objectData);
    $objectId = $object['data']['id'];
    echo "✅ 物件已創建: {$objectId}\n";
    
    // 2. 上傳檔案內容
    $uploadResult = $client->uploadFile($userId, '/path/to/document.pdf', $objectId);
    echo "✅ 檔案內容已上傳完成\n";
    
    // 3. 驗證上傳結果
    $objectInfo = $client->getObjectInfo($objectId, $userId);
    echo "📄 檔案大小: " . $objectInfo['data']['size'] . " bytes\n";
    
} catch (FileServiceException $e) {
    echo "❌ 上傳失敗: " . $e->getMessage() . "\n";
}
```

## 📖 範例程式

### 完整的檔案操作流程

```php
<?php

require_once 'vendor/autoload.php';

use FDMC\FileServiceClient\FileServiceClient;
use FDMC\FileServiceClient\FileServiceException;

$client = new FileServiceClient([
    'base_uri' => 'https://api.fileservice.com',
    'timeout' => 60
]);

$client->setHeaders(['Authorization' => 'Bearer ' . $token]);

$companyId = 1;
$scope = 'projects';
$userId = 123;

try {
    // 1. 建立專案資料夾
    $projectFolder = $client->createObject($userId, $client->buildObjectData(
        company_id: $companyId,
        scope: $scope,
        name: '新專案',
        type: 'folder'
    ));
    
    $folderId = $projectFolder['data']['id'];
    echo "✅ 已建立專案資料夾: {$folderId}\n";
    
    // 2. 在專案資料夹中建立文件
    $document = $client->createObject($userId, $client->buildObjectData(
        company_id: $companyId,
        scope: $scope,
        name: '專案計劃.docx',
        type: 'file',
        parent: $folderId,
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ));
    
    $documentId = $document['data']['id'];
    echo "✅ 已建立文件: {$documentId}\n";
    
    // 3. 重命名文件
    $client->renameObject($documentId, $userId, '專案計劃-v1.0.docx');
    echo "✅ 已重命名文件\n";
    
    // 4. 獲取資料夾內容
    $contents = $client->listObjects($userId, $folderId);
    echo "� 資尋料夾包含 " . count($contents['data']) . " 個項目\n";
    
    // 5. 搜尋文件
    $searchResults = $client->searchObjects($companyId, $scope, $userId, '專案計劃');
    echo "� 搜尋到  " . count($searchResults['data']) . " 個結果\n";
    
    // 6. 獲取版本歷史
    $versions = $client->getVersionList($documentId, $userId);
    echo "📝 文件有 " . count($versions['data']) . " 個版本\n";
    
    // 7. 複製文件到其他位置
    $copyResult = $client->copyObjects($documentId, $userId, $targetFolderId, '專案計劃-副本.docx', 'ignore');
    echo "✅ 已複製文件\n";
    
    // 8. 移動文件
    $moveResult = $client->moveObjects($documentId, $userId, $newFolderId, 'overwrite');
    echo "✅ 已移動文件\n";
    
} catch (FileServiceException $e) {
    echo "❌ 錯誤: " . $e->getMessage() . "\n";
}
```

更多詳細範例請參考：
- 📋 [基本使用範例](examples/http_client_usage.php) - HTTP 客戶端使用示例
- 🚀 [檔案上傳快速指南](examples/quick_upload_guide.php) - 簡潔的上傳流程指南
- 📤 [檔案上傳完整範例](examples/upload_example.php) - 各種上傳方式的詳細示例
- 🏗️ [架構設計比較](examples/architecture_comparison.php) - 不同架構方案的比較分析
- 💾 [儲存管理範例](examples/storage_example.php) - 儲存後端管理示例
- 📖 [API 使用說明](API_USAGE_EXAMPLES.md) - 詳細的 API 使用文檔

## 🔧 進階配置

### 自定義 HTTP 客戶端

```php
use FDMC\FileServiceClient\Http\HttpClientInterface;
use FDMC\FileServiceClient\Http\GuzzleHttpClient;

// 建立自定義的 HTTP 客戶端
class CustomHttpClient extends GuzzleHttpClient {
    protected function parseJsonResponse(\Psr\Http\Message\ResponseInterface $response): array {
        // 自定義回應解析邏輯
        $data = parent::parseJsonResponse($response);
        
        // 添加日誌記錄
        error_log("API 回應: " . json_encode($data));
        
        return $data;
    }
}

// 使用自定義客戶端
$customHttpClient = new CustomHttpClient([
    'base_uri' => 'https://api.example.com',
    'timeout' => 60
]);
$client = new FileServiceClient([], $customHttpClient);
```

### 中間件支援

```php
// 如果需要添加請求/回應中間件，可以直接操作底層的 Guzzle 客戶端
$httpClient = $client->getHttpClient();
if ($httpClient instanceof \FDMC\FileServiceClient\Http\GuzzleHttpClient) {
    $guzzle = $httpClient->getGuzzleClient();
    
    // 添加 Guzzle 中間件
    $handler = $guzzle->getConfig('handler');
    $handler->push(\GuzzleHttp\Middleware::log($logger, $messageFormatter));
}
```

### 環境變數配置

```php
// 使用環境變數配置客戶端
$client = new FileServiceClient([
    'base_uri' => $_ENV['FILE_SERVICE_BASE_URL'] ?? 'https://api.fileservice.com',
    'timeout' => (int)($_ENV['FILE_SERVICE_TIMEOUT'] ?? 60),
    'verify' => filter_var($_ENV['FILE_SERVICE_SSL_VERIFY'] ?? 'true', FILTER_VALIDATE_BOOLEAN),
    'headers' => [
        'Authorization' => 'Bearer ' . $_ENV['FILE_SERVICE_TOKEN'],
        'User-Agent' => $_ENV['APP_NAME'] . '/' . $_ENV['APP_VERSION']
    ]
]);
```

## 📊 效能考量

### 批次操作

```php
// 批次處理多個檔案時，建議使用非同步方式
$promises = [];
foreach ($fileIds as $fileId) {
    $promises[] = $client->getObjectInfo($fileId, $userId);
}

// 等待所有請求完成
$results = Promise\settle($promises)->wait();
```

### 快取建議

```php
// 建議對經常存取的資料進行快取
class CachedFileServiceClient extends FileServiceClient {
    private $cache;
    
    public function __construct(array $options = [], $httpClient = null, $cache = null) {
        parent::__construct($options, $httpClient);
        $this->cache = $cache;
    }
    
    public function getObjectInfo(string $objectId, int $userId): array {
        if (!$this->cache) {
            return parent::getObjectInfo($objectId, $userId);
        }
        
        $cacheKey = "object_info_{$objectId}_{$userId}";
        
        if ($cached = $this->cache->get($cacheKey)) {
            return $cached;
        }
        
        $result = parent::getObjectInfo($objectId, $userId);
        $this->cache->set($cacheKey, $result, 300); // 快取 5 分鐘
        
        return $result;
    }
}

// 使用快取客戶端
$cache = new \Psr\SimpleCache\CacheInterface(); // 使用你的快取實現
$client = new CachedFileServiceClient([
    'base_uri' => 'https://api.fileservice.com'
], null, $cache);
```


---

<div align="center">

**[⬆ 回到頂部](#file-service-client)**

</div> 
