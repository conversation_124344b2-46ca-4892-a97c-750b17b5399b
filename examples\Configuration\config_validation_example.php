<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\ConfigValidator;
use FDMC\FileServiceClient\ConfigurationException;

echo "=== 配置驗證系統使用範例 ===\n\n";

// 建立配置驗證器
$validator = new ConfigValidator();

// 範例 1：有效的配置
echo "1. 驗證有效配置\n";
echo "================\n";

$validConfig = [
    'baseUrl' => 'https://api.example.com',
    'http' => [
        'timeout' => 30,
        'connect_timeout' => 10,
        'read_timeout' => 60,
        'verify' => false,
        'http_errors' => false,
    ],
    'upload' => [
        'timeout' => 120,
        'max_file_size' => 104857600,
        'allowed_mime_types' => ['image/jpeg', 'image/png', 'application/pdf'],
    ],
    'download' => [
        'timeout' => 300,
        'stream_buffer_size' => 8192,
        'large_file_threshold' => 52428800,
    ],
    'cache' => [
        'enabled' => false,
        'driver' => 'file',
        'ttl' => 3600,
    ],
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'channel' => 'default',
    ],
    'retry' => [
        'max_attempts' => 3,
        'delay' => 1000,
        'multiplier' => 2.0,
    ],
];

$result = $validator->validate($validConfig);

if ($result->isValid()) {
    echo "✅ 配置驗證通過！\n";
    echo "摘要：" . $result->getSummary() . "\n\n";
} else {
    echo "❌ 配置驗證失敗\n";
    echo $result->getDetailedReport() . "\n\n";
}

// 範例 2：包含錯誤的配置
echo "2. 驗證包含錯誤的配置\n";
echo "======================\n";

$invalidConfig = [
    'baseUrl' => 'invalid-url', // 無效的 URL
    'http' => [
        'timeout' => 500, // 超出最大值
        'connect_timeout' => 0, // 小於最小值
        'read_timeout' => 60,
        'verify' => 'not-boolean', // 型別錯誤
        'http_errors' => false,
    ],
    'upload' => [
        'timeout' => 120,
        'max_file_size' => 104857600,
        'allowed_mime_types' => [], // 空陣列
    ],
    'download' => [
        'timeout' => 300,
        'stream_buffer_size' => 8192,
        'large_file_threshold' => 52428800,
    ],
    'cache' => [
        'enabled' => false,
        'driver' => 'invalid-driver', // 無效的枚舉值
        'ttl' => 3600,
    ],
    'logging' => [
        'enabled' => true,
        'level' => 'invalid-level', // 無效的枚舉值
        'channel' => 'default',
    ],
    'retry' => [
        'max_attempts' => 3,
        'delay' => 1000,
        'multiplier' => 2.0,
    ],
];

$result = $validator->validate($invalidConfig);

if ($result->isValid()) {
    echo "✅ 配置驗證通過！\n";
} else {
    echo "❌ 配置驗證失敗\n";
    echo $result->getDetailedReport() . "\n\n";
}

// 範例 3：缺少必要配置項目
echo "3. 驗證缺少必要配置項目\n";
echo "========================\n";

$incompleteConfig = [
    // 缺少 baseUrl
    'http' => [
        'timeout' => 30,
        'connect_timeout' => 10,
        'read_timeout' => 60,
        'verify' => false,
        'http_errors' => false,
    ],
    // 缺少其他必要配置...
];

$result = $validator->validate($incompleteConfig);

if ($result->isValid()) {
    echo "✅ 配置驗證通過！\n";
} else {
    echo "❌ 配置驗證失敗\n";
    echo "錯誤數量：" . $result->getErrorCount() . "\n";
    echo "錯誤摘要：" . $result->getSummary() . "\n\n";
    
    // 顯示前 5 個錯誤
    $errors = $result->getFormattedErrors();
    echo "前 5 個錯誤：\n";
    foreach (array_slice($errors, 0, 5) as $error) {
        echo "  • " . $error . "\n";
    }
    echo "\n";
}

// 範例 4：使用 ConfigurationException
echo "4. 配置例外處理範例\n";
echo "====================\n";

try {
    // 模擬配置驗證失敗的情況
    $result = $validator->validate($invalidConfig);
    
    if (!$result->isValid()) {
        throw ConfigurationException::validationFailed($result);
    }
    
    echo "配置驗證通過\n";
    
} catch (ConfigurationException $e) {
    echo "捕獲配置例外：\n";
    echo "錯誤碼：" . $e->getCode() . "\n";
    echo "錯誤描述：" . $e->getErrorDescription() . "\n";
    echo "錯誤訊息：" . $e->getMessage() . "\n\n";
    
    // 獲取詳細報告
    echo "詳細報告：\n";
    echo $e->getDetailedReport() . "\n\n";
    
    // 獲取修復建議
    $suggestions = $e->getRepairSuggestions();
    if (!empty($suggestions)) {
        echo "修復建議：\n";
        foreach ($suggestions as $key => $suggestion) {
            echo "  [{$key}] {$suggestion}\n";
        }
    }
}

// 範例 5：檢查驗證規則
echo "\n5. 檢查驗證規則\n";
echo "================\n";

$rules = $validator->getValidationRules();
echo "總共定義了 " . count($rules) . " 個驗證規則\n\n";

echo "部分驗證規則範例：\n";
$sampleRules = ['baseUrl', 'http.timeout', 'logging.level', 'cache.driver'];

foreach ($sampleRules as $key) {
    if (isset($rules[$key])) {
        $rule = $rules[$key];
        echo "  {$key}:\n";
        echo "    - 類型: {$rule['type']}\n";
        echo "    - 必填: " . ($rule['required'] ? '是' : '否') . "\n";
        echo "    - 描述: {$rule['description']}\n";
        
        if (isset($rule['min'])) {
            echo "    - 最小值: {$rule['min']}\n";
        }
        if (isset($rule['max'])) {
            echo "    - 最大值: {$rule['max']}\n";
        }
        if (isset($rule['values'])) {
            echo "    - 允許值: " . implode(', ', $rule['values']) . "\n";
        }
        echo "\n";
    }
}

// 範例 6：ValidationResult 的各種方法
echo "6. ValidationResult 方法展示\n";
echo "=============================\n";

$result = $validator->validate($invalidConfig);

echo "基本資訊：\n";
echo "  - 是否有效: " . ($result->isValid() ? '是' : '否') . "\n";
echo "  - 是否有錯誤: " . ($result->hasErrors() ? '是' : '否') . "\n";
echo "  - 錯誤數量: " . $result->getErrorCount() . "\n\n";

echo "錯誤分析：\n";
$errorTypes = [];
foreach ($result->getErrors() as $error) {
    $errorTypes[$error['type']] = ($errorTypes[$error['type']] ?? 0) + 1;
}

foreach ($errorTypes as $type => $count) {
    echo "  - {$type}: {$count} 個\n";
}

echo "\n轉換為 JSON：\n";
echo $result->toJson() . "\n";

echo "\n=== 範例結束 ===\n";