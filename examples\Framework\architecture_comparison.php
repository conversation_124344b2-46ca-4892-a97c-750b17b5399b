<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Fdmc\FileServiceClient\FileServiceClient;
use Fdmc\FileServiceClient\Http\HttpClientInterface;
use Fdmc\FileServiceClient\Http\GuzzleHttpClient;
use Fdmc\FileServiceClient\FileServiceException;

/**
 * 架構設計比較與建議
 * 展示不同架構方案的優缺點和使用場景
 */

echo "=== 架構設計方案比較 ===\n\n";

// ========================================
// 方案 1: 當前實現 - 內部 HTTP 層（推薦）
// ========================================
echo "方案 1: 內部 HTTP 層（推薦）\n";
echo "----------------------------------------\n";

try {
    // 標準使用方式 - 使用者只需要知道 FileServiceClient
    $client = new FileServiceClient('https://api.example.com', [
        'timeout' => 60,
        'verify' => true,
    ]);

    $client->setHeaders(['Authorization' => 'Bearer token']);

    echo "✅ 簡單易用：只需要了解一個主要類別\n";
    echo "✅ 封裝性好：HTTP 細節被隱藏\n";

    // 進階使用方式 - 依賴注入（用於測試）
    $mockHttpClient = new class implements HttpClientInterface {
        public function sendJsonRequest(string $method, string $endpoint, array $options = []): array
        {
            return ['success' => true, 'data' => ['mock' => 'data']];
        }

        public function sendRawRequest(string $method, string $endpoint, array $options = []): \Psr\Http\Message\ResponseInterface
        {
            return new \GuzzleHttp\Psr7\Response(200, [], 'mock content');
        }

        public function setHeaders(array $headers): void
        {
        }
        public function getBaseUrl(): string
        {
            return 'mock://api';
        }
    };

    $testClient = new FileServiceClient('', [], $mockHttpClient);
    echo "✅ 可測試性：支援依賴注入\n";

} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
}

echo "\n";

// ========================================
// 方案 2: 分層架構
// ========================================
echo "方案 2: 分層架構\n";
echo "----------------------------------------\n";

/**
 * 這種方案將功能分為明確的層級：
 * 
 * 1. Transport Layer (Http/)
 *    - HttpClientInterface
 *    - GuzzleHttpClient
 *    - 其他 HTTP 實現 (CurlHttpClient, etc.)
 * 
 * 2. Service Layer (Services/)
 *    - FileService
 *    - ObjectService  
 *    - VersionService
 * 
 * 3. Client Layer
 *    - FileServiceClient (主要入口)
 */

echo "✅ 職責分離清晰\n";
echo "✅ 易於擴展新功能\n";
echo "✅ 可以單獨測試每一層\n";
echo "❌ 複雜度較高\n";
echo "❌ 學習成本增加\n";

echo "\n";

// ========================================
// 方案 3: 聚合根模式
// ========================================
echo "方案 3: 聚合根模式\n";
echo "----------------------------------------\n";

/**
 * 將所有功能聚合在一個主要類別中：
 * 
 * FileServiceClient
 * ├── ObjectManager
 * ├── VersionManager  
 * ├── SearchManager
 * └── HttpTransport (private)
 */

echo "✅ API 一致性高\n";
echo "✅ 使用者體驗好\n";
echo "✅ 易於理解和使用\n";
echo "❌ 單一類別可能過大\n";
echo "❌ 違反單一職責原則\n";

echo "\n";

// ========================================
// 推薦方案分析
// ========================================
echo "=== 推薦方案分析 ===\n";
echo "----------------------------------------\n";

echo "🎯 對於檔案服務客戶端，我推薦【方案 1: 內部 HTTP 層】\n\n";

echo "理由：\n";
echo "1. 📦 封裝性好：\n";
echo "   - HTTP 細節對使用者隱藏\n";
echo "   - 提供簡潔的 API 介面\n\n";

echo "2. 🧪 可測試性佳：\n";
echo "   - 支援依賴注入\n";
echo "   - 可以輕鬆 mock HTTP 層\n\n";

echo "3. 🔧 可維護性高：\n";
echo "   - HTTP 實現可以獨立變更\n";
echo "   - 業務邏輯與傳輸層解耦\n\n";

echo "4. 🚀 易於使用：\n";
echo "   - 使用者只需要學習一個主要類別\n";
echo "   - 提供合理的預設配置\n\n";

echo "5. 🔍 擴展性好：\n";
echo "   - 可以輕鬆添加新的 HTTP 實現\n";
echo "   - 支援中間件、重試等功能\n\n";

// ========================================
// 最佳實踐建議
// ========================================
echo "=== 最佳實踐建議 ===\n";
echo "----------------------------------------\n";

echo "📋 目錄結構建議：\n";
echo "src/\n";
echo "├── FileServiceClient.php     # 主要入口\n";
echo "├── FileServiceException.php  # 例外類別\n";
echo "├── Http/                     # HTTP 傳輸層（內部）\n";
echo "│   ├── HttpClientInterface.php\n";
echo "│   └── GuzzleHttpClient.php\n";
echo "└── Models/                   # 資料模型（可選）\n";
echo "    ├── FileObject.php\n";
echo "    └── Version.php\n\n";

echo "🎨 API 設計原則：\n";
echo "1. 對外簡單，對內複雜\n";
echo "2. 預設配置要合理\n";
echo "3. 錯誤訊息要清晰\n";
echo "4. 支援依賴注入用於測試\n";
echo "5. 向後相容性很重要\n\n";

echo "✨ 結論：\n";
echo "當前的重構方向是正確的，但建議：\n";
echo "1. 將 HTTP 相關類別移到 Http/ 子命名空間\n";
echo "2. 使用介面定義 HTTP 客戶端契約\n";
echo "3. FileServiceClient 保持為主要的公開 API\n";
echo "4. HTTP 客戶端作為內部實現細節\n";