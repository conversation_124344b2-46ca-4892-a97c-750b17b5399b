<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\ConfigurationException;
use FDMC\FileServiceClient\TypeConversionException;
use FDMC\FileServiceClient\ConfigLogger;
use FDMC\FileServiceClient\ValidationResult;

echo "=== 配置例外處理系統範例 ===\n\n";

// 1. 設定日誌記錄器
echo "1. 設定日誌記錄器\n";
$logger = ConfigLogger::getInstance();
$logger->setMinLevel(ConfigLogger::LEVEL_DEBUG);

// 添加自訂日誌處理器
$logger->addHandler(function (array $record): void {
    $datetime = $record['datetime']->format('H:i:s');
    $level = $record['level_name'];
    $message = $record['message'];
    echo "[{$datetime}] {$level}: {$message}\n";
});

echo "日誌記錄器已設定完成\n\n";

// 2. 演示基本配置例外
echo "2. 演示基本配置例外\n";
try {
    throw ConfigurationException::fileNotReadable('/nonexistent/config.php');
} catch (ConfigurationException $e) {
    echo "捕獲例外：{$e->getMessage()}\n";
    echo "錯誤碼：{$e->getCode()}\n";
    echo "錯誤描述：{$e->getErrorDescription()}\n";
    echo "修復建議：\n";
    foreach ($e->getRepairSuggestions() as $suggestion) {
        echo "  • {$suggestion}\n";
    }
}
echo "\n";

// 3. 演示型別轉換例外
echo "3. 演示型別轉換例外\n";
try {
    throw TypeConversionException::booleanConversionFailed('enabled', 'maybe');
} catch (TypeConversionException $e) {
    echo "捕獲型別轉換例外：{$e->getMessage()}\n";
    echo "配置鍵：{$e->getConfigKey()}\n";
    echo "原始值：" . var_export($e->getOriginalValue(), true) . "\n";
    echo "目標型別：{$e->getTargetType()}\n";
    echo "詳細報告：\n";
    echo $e->getDetailedReport() . "\n";
}
echo "\n";

// 4. 演示驗證失敗例外
echo "4. 演示驗證失敗例外\n";
$errors = [
    [
        'key' => 'baseUrl',
        'message' => '無效的 URL 格式',
        'type' => 'format',
        'description' => '必須是有效的 HTTP 或 HTTPS URL',
        'suggestion' => '使用格式如：https://api.example.com'
    ],
    [
        'key' => 'timeout',
        'message' => '值超出允許範圍',
        'type' => 'range',
        'description' => '必須在 1-300 秒之間',
        'suggestion' => '設定為 1 到 300 之間的整數'
    ]
];

$validationResult = new ValidationResult(false, $errors);

try {
    throw ConfigurationException::validationFailed($validationResult);
} catch (ConfigurationException $e) {
    echo "捕獲驗證失敗例外：{$e->getMessage()}\n";
    echo "驗證結果摘要：{$e->getValidationResult()->getSummary()}\n";
    echo "詳細報告：\n";
    echo $e->getDetailedReport() . "\n";
}
echo "\n";

// 5. 演示自動日誌記錄
echo "5. 演示自動日誌記錄\n";
echo "建立例外時自動記錄日誌：\n";
$exception = ConfigurationException::typeConversionFailed('timeout', 'invalid', 'integer');
echo "例外已建立並記錄到日誌\n";
echo "是否已記錄：" . ($exception->isLogged() ? '是' : '否') . "\n\n";

// 6. 演示手動日誌記錄
echo "6. 演示手動日誌記錄\n";
$exception = ConfigurationException::fileNotFound('/missing/config.php', false);
echo "例外已建立但未自動記錄\n";
echo "是否已記錄：" . ($exception->isLogged() ? '是' : '否') . "\n";

echo "手動記錄例外：\n";
$exception->logException(ConfigLogger::LEVEL_WARNING);
echo "是否已記錄：" . ($exception->isLogged() ? '是' : '否') . "\n\n";

// 7. 演示不同類型的例外
echo "7. 演示不同類型的例外\n";

$exceptionTypes = [
    'fileNotFound' => fn() => ConfigurationException::fileNotFound('/missing.php', false),
    'syntaxError' => fn() => ConfigurationException::syntaxError('/bad.php', 'Unexpected token', false),
    'permissionDenied' => fn() => ConfigurationException::permissionDenied('/restricted.php', false),
    'valueOutOfRange' => fn() => ConfigurationException::valueOutOfRange('timeout', 1000, 1, 300, false),
    'invalidEnumValue' => fn() => ConfigurationException::invalidEnumValue('level', 'invalid', ['debug', 'info', 'error'], false),
    'invalidUrl' => fn() => ConfigurationException::invalidUrl('baseUrl', 'not-a-url', false),
    'mergeConflict' => fn() => ConfigurationException::mergeConflict('config.key', 'string', ['array'], false),
    'circularDependency' => fn() => ConfigurationException::circularDependency(['a', 'b', 'c', 'a'], false),
];

foreach ($exceptionTypes as $type => $factory) {
    $exception = $factory();
    echo "{$type}: {$exception->getErrorDescription()}\n";
}
echo "\n";

// 8. 演示型別轉換例外的各種類型
echo "8. 演示型別轉換例外的各種類型\n";

$typeConversionTypes = [
    'boolean' => fn() => TypeConversionException::booleanConversionFailed('enabled', 'maybe'),
    'integer' => fn() => TypeConversionException::integerConversionFailed('count', 'not-a-number'),
    'float' => fn() => TypeConversionException::floatConversionFailed('ratio', 'invalid-float'),
    'array' => fn() => TypeConversionException::arrayConversionFailed('headers', '{invalid}', 'JSON 語法錯誤'),
    'string' => fn() => TypeConversionException::stringConversionFailed('name', ['complex', 'array']),
    'unsupported' => fn() => TypeConversionException::unsupportedType('resource', 'value', 'resource'),
];

foreach ($typeConversionTypes as $type => $factory) {
    $exception = $factory();
    echo "{$type}: {$exception->getErrorDescription()}\n";
}
echo "\n";

// 9. 演示日誌級別
echo "9. 演示日誌級別\n";
$logger->debug('這是除錯訊息');
$logger->info('這是資訊訊息');
$logger->warning('這是警告訊息');
$logger->error('這是錯誤訊息');
$logger->critical('這是嚴重錯誤訊息');
echo "\n";

// 10. 演示配置相關的日誌方法
echo "10. 演示配置相關的日誌方法\n";
$logger->logConfigLoad('/path/to/config.php', ['keys_loaded' => 5]);
$logger->logConfigValidation(true, ['validated_keys' => 3]);
$logger->logConfigValidation(false, ['errors' => 2]);
$logger->logTypeConversion('timeout', '30', 30, 'integer');
$logger->logTypeConversionFailure('enabled', 'maybe', 'boolean', '無效的布林值');

echo "\n=== 範例完成 ===\n";