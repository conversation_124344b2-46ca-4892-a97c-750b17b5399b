<?php

require 'vendor/autoload.php';

echo "Testing autoloader...\n";

// 測試 GlobalConfigManager
if (class_exists('FDMC\FileServiceClient\GlobalConfigManager')) {
    echo "✓ GlobalConfigManager: OK\n";
} else {
    echo "✗ GlobalConfigManager: NOT FOUND\n";
}

// 測試 ConfigLoader
if (class_exists('FDMC\FileServiceClient\ConfigLoader')) {
    echo "✓ ConfigLoader: OK\n";
} else {
    echo "✗ ConfigLoader: NOT FOUND\n";
}

// 測試 ConfigLogger
if (class_exists('FDMC\FileServiceClient\ConfigLogger')) {
    echo "✓ ConfigLogger: OK\n";
} else {
    echo "✗ ConfigLogger: NOT FOUND\n";
}

echo "Test completed.\n";
