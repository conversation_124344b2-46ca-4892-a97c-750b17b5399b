<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Processing;

/**
 * 配置驗證器
 * 
 * 負責驗證配置檔案的格式和內容正確性，提供詳細的錯誤訊息和修復建議。
 */
class ConfigValidator
{
    /**
     * 驗證結果
     */
    private array $validationResults = [];

    /**
     * 驗證規則定義
     */
    private array $validationRules = [
        'baseUrl' => [
            'required' => true,
            'type' => 'url',
            'description' => 'API 基礎 URL',
        ],
        'http.timeout' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'max' => 300,
            'description' => 'HTTP 請求超時時間（秒）',
        ],
        'http.connect_timeout' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'max' => 60,
            'description' => 'HTTP 連接超時時間（秒）',
        ],
        'http.read_timeout' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'max' => 600,
            'description' => 'HTTP 讀取超時時間（秒）',
        ],
        'http.verify' => [
            'required' => true,
            'type' => 'boolean',
            'description' => 'SSL 驗證開關',
        ],
        'http.http_errors' => [
            'required' => true,
            'type' => 'boolean',
            'description' => 'HTTP 錯誤處理開關',
        ],
        'upload.timeout' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'max' => 600,
            'description' => '檔案上傳超時時間（秒）',
        ],
        'upload.max_file_size' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'description' => '最大檔案大小（位元組）',
        ],
        'upload.allowed_mime_types' => [
            'required' => true,
            'type' => 'array',
            'description' => '允許的檔案類型列表',
        ],
        'download.timeout' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'max' => 3600,
            'description' => '檔案下載超時時間（秒）',
        ],
        'download.stream_buffer_size' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1024,
            'description' => '串流緩衝區大小（位元組）',
        ],
        'download.large_file_threshold' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'description' => '大檔案閾值（位元組）',
        ],
        'cache.enabled' => [
            'required' => true,
            'type' => 'boolean',
            'description' => '快取功能開關',
        ],
        'cache.driver' => [
            'required' => true,
            'type' => 'enum',
            'values' => ['file', 'redis', 'memcached', 'array'],
            'description' => '快取驅動類型',
        ],
        'cache.ttl' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'description' => '快取過期時間（秒）',
        ],
        'logging.enabled' => [
            'required' => true,
            'type' => 'boolean',
            'description' => '日誌功能開關',
        ],
        'logging.level' => [
            'required' => true,
            'type' => 'enum',
            'values' => ['debug', 'info', 'warning', 'error'],
            'description' => '日誌等級',
        ],
        'logging.channel' => [
            'required' => true,
            'type' => 'string',
            'description' => '日誌通道名稱',
        ],
        'retry.max_attempts' => [
            'required' => true,
            'type' => 'integer',
            'min' => 1,
            'max' => 10,
            'description' => '最大重試次數',
        ],
        'retry.delay' => [
            'required' => true,
            'type' => 'integer',
            'min' => 100,
            'description' => '重試延遲時間（毫秒）',
        ],
        'retry.multiplier' => [
            'required' => true,
            'type' => 'number',
            'min' => 1,
            'description' => '重試延遲倍數',
        ],
    ];

    /**
     * 驗證完整配置
     *
     * @param array $config 配置陣列
     * @return ValidationResult 驗證結果
     */
    public function validate(array $config): ValidationResult
    {
        $this->validationResults = [];

        foreach ($this->validationRules as $key => $rule) {
            $value = $this->getNestedValue($config, $key);
            $this->validateRule($key, $value, $rule);
        }

        return new ValidationResult(
            empty($this->validationResults),
            $this->validationResults
        );
    }

    /**
     * 驗證單一規則
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param array $rule 驗證規則
     * @return void
     */
    public function validateRule(string $key, mixed $value, array $rule): void
    {
        // 檢查必填項目
        if ($rule['required'] && ($value === null || $value === '')) {
            $this->addValidationError($key, 'required', "配置項目 '{$key}' 為必填項目", $rule);
            return;
        }

        // 如果值為空且非必填，跳過驗證
        if ($value === null || $value === '') {
            return;
        }

        // 型別驗證
        if (!$this->validateType($key, $value, $rule)) {
            return;
        }

        // 範圍驗證
        $this->validateRange($key, $value, $rule);

        // 枚舉值驗證
        $this->validateEnum($key, $value, $rule);

        // URL 驗證
        $this->validateUrl($key, $value, $rule);

        // 陣列驗證
        $this->validateArray($key, $value, $rule);
    }

    /**
     * 獲取驗證規則定義
     *
     * @return array 驗證規則陣列
     */
    public function getValidationRules(): array
    {
        return $this->validationRules;
    }

    /**
     * 獲取巢狀配置值
     *
     * @param array $config 配置陣列
     * @param string $key 配置鍵（支援點號語法）
     * @return mixed 配置值
     */
    private function getNestedValue(array $config, string $key): mixed
    {
        $keys = explode('.', $key);
        $value = $config;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return null;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * 驗證型別
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param array $rule 驗證規則
     * @return bool 驗證是否通過
     */
    private function validateType(string $key, mixed $value, array $rule): bool
    {
        $expectedType = $rule['type'];
        $actualType = $this->getValueType($value);

        if (!$this->isTypeMatch($actualType, $expectedType)) {
            $this->addValidationError(
                $key,
                'type',
                "配置項目 '{$key}' 型別錯誤，期望 {$expectedType}，實際 {$actualType}",
                $rule,
                $this->getTypeSuggestion($expectedType, $value)
            );
            return false;
        }

        return true;
    }

    /**
     * 驗證範圍
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param array $rule 驗證規則
     * @return void
     */
    private function validateRange(string $key, mixed $value, array $rule): void
    {
        if (!is_numeric($value)) {
            return;
        }

        if (isset($rule['min']) && $value < $rule['min']) {
            $this->addValidationError(
                $key,
                'min',
                "配置項目 '{$key}' 值 {$value} 小於最小值 {$rule['min']}",
                $rule,
                "建議設定為 {$rule['min']} 或更大的值"
            );
        }

        if (isset($rule['max']) && $value > $rule['max']) {
            $this->addValidationError(
                $key,
                'max',
                "配置項目 '{$key}' 值 {$value} 大於最大值 {$rule['max']}",
                $rule,
                "建議設定為 {$rule['max']} 或更小的值"
            );
        }
    }

    /**
     * 驗證枚舉值
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param array $rule 驗證規則
     * @return void
     */
    private function validateEnum(string $key, mixed $value, array $rule): void
    {
        if (!isset($rule['values']) || $rule['type'] !== 'enum') {
            return;
        }

        if (!in_array($value, $rule['values'], true)) {
            $allowedValues = implode(', ', $rule['values']);
            $this->addValidationError(
                $key,
                'enum',
                "配置項目 '{$key}' 值 '{$value}' 不在允許的值列表中",
                $rule,
                "允許的值：{$allowedValues}"
            );
        }
    }

    /**
     * 驗證 URL
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param array $rule 驗證規則
     * @return void
     */
    private function validateUrl(string $key, mixed $value, array $rule): void
    {
        if ($rule['type'] !== 'url') {
            return;
        }

        if (!is_string($value) || !filter_var($value, FILTER_VALIDATE_URL)) {
            $this->addValidationError(
                $key,
                'url',
                "配置項目 '{$key}' 不是有效的 URL 格式",
                $rule,
                "請確保 URL 包含協議（如 http:// 或 https://）"
            );
        }
    }

    /**
     * 驗證陣列
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param array $rule 驗證規則
     * @return void
     */
    private function validateArray(string $key, mixed $value, array $rule): void
    {
        if ($rule['type'] !== 'array') {
            return;
        }

        if (!is_array($value)) {
            $this->addValidationError(
                $key,
                'array',
                "配置項目 '{$key}' 必須是陣列",
                $rule,
                "請使用陣列格式，例如：['value1', 'value2']"
            );
            return;
        }

        if (empty($value)) {
            $this->addValidationError(
                $key,
                'array_empty',
                "配置項目 '{$key}' 陣列不能為空",
                $rule,
                "請至少提供一個有效的值"
            );
        }
    }

    /**
     * 獲取值的型別
     *
     * @param mixed $value 值
     * @return string 型別名稱
     */
    private function getValueType(mixed $value): string
    {
        return match (true) {
            is_bool($value) => 'boolean',
            is_int($value) => 'integer',
            is_float($value) => 'number',
            is_string($value) => 'string',
            is_array($value) => 'array',
            is_null($value) => 'null',
            default => gettype($value)
        };
    }

    /**
     * 檢查型別是否匹配
     *
     * @param string $actualType 實際型別
     * @param string $expectedType 期望型別
     * @return bool 是否匹配
     */
    private function isTypeMatch(string $actualType, string $expectedType): bool
    {
        if ($actualType === $expectedType) {
            return true;
        }

        // 數字型別的特殊處理
        if ($expectedType === 'number' && in_array($actualType, ['integer', 'float'])) {
            return true;
        }

        // URL 型別實際上是字串
        if ($expectedType === 'url' && $actualType === 'string') {
            return true;
        }

        // 枚舉型別實際上是字串
        if ($expectedType === 'enum' && $actualType === 'string') {
            return true;
        }

        return false;
    }

    /**
     * 獲取型別修復建議
     *
     * @param string $expectedType 期望型別
     * @param mixed $value 當前值
     * @return string 修復建議
     */
    private function getTypeSuggestion(string $expectedType, mixed $value): string
    {
        return match ($expectedType) {
            'boolean' => "請使用 true 或 false，當前值：" . var_export($value, true),
            'integer' => "請使用整數，當前值：" . var_export($value, true),
            'number' => "請使用數字，當前值：" . var_export($value, true),
            'string' => "請使用字串，當前值：" . var_export($value, true),
            'array' => "請使用陣列格式，當前值：" . var_export($value, true),
            'url' => "請使用有效的 URL 格式（包含 http:// 或 https://），當前值：" . var_export($value, true),
            default => "請檢查值的格式，當前值：" . var_export($value, true)
        };
    }

    /**
     * 添加驗證錯誤
     *
     * @param string $key 配置鍵
     * @param string $type 錯誤類型
     * @param string $message 錯誤訊息
     * @param array $rule 驗證規則
     * @param string|null $suggestion 修復建議
     * @return void
     */
    private function addValidationError(
        string $key,
        string $type,
        string $message,
        array $rule,
        ?string $suggestion = null
    ): void {
        $this->validationResults[] = [
            'key' => $key,
            'type' => $type,
            'message' => $message,
            'description' => $rule['description'] ?? '',
            'suggestion' => $suggestion,
            'rule' => $rule,
        ];
    }
}