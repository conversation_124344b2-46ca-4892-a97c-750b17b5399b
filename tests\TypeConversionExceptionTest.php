<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\Exceptions\TypeConversionException;
use FDMC\FileServiceClient\Logging\ConfigLogger;
use PHPUnit\Framework\TestCase;

/**
 * 型別轉換例外測試
 */
class TypeConversionExceptionTest extends TestCase
{
    protected function setUp(): void
    {
        // 清除日誌處理器以避免測試間干擾
        ConfigLogger::getInstance()->clearHandlers();
    }

    public function testBooleanConversionFailedException(): void
    {
        $key = 'enabled';
        $value = 'maybe';
        $exception = TypeConversionException::booleanConversionFailed($key, $value);

        $this->assertInstanceOf(TypeConversionException::class, $exception);
        $this->assertEquals(TypeConversionException::TYPE_BOOLEAN_CONVERSION_FAILED, $exception->getCode());
        $this->assertEquals($key, $exception->getConfigKey());
        $this->assertEquals($value, $exception->getOriginalValue());
        $this->assertEquals('boolean', $exception->getTargetType());
        $this->assertStringContainsString('無法將', $exception->getMessage());
        $this->assertStringContainsString('轉換為布林值', $exception->getMessage());
    }

    public function testIntegerConversionFailedException(): void
    {
        $key = 'timeout';
        $value = 'not-a-number';
        $exception = TypeConversionException::integerConversionFailed($key, $value);

        $this->assertInstanceOf(TypeConversionException::class, $exception);
        $this->assertEquals(TypeConversionException::TYPE_INTEGER_CONVERSION_FAILED, $exception->getCode());
        $this->assertEquals($key, $exception->getConfigKey());
        $this->assertEquals($value, $exception->getOriginalValue());
        $this->assertEquals('integer', $exception->getTargetType());
        $this->assertStringContainsString('轉換為整數', $exception->getMessage());
    }

    public function testFloatConversionFailedException(): void
    {
        $key = 'ratio';
        $value = 'invalid-float';
        $exception = TypeConversionException::floatConversionFailed($key, $value);

        $this->assertInstanceOf(TypeConversionException::class, $exception);
        $this->assertEquals(TypeConversionException::TYPE_FLOAT_CONVERSION_FAILED, $exception->getCode());
        $this->assertEquals($key, $exception->getConfigKey());
        $this->assertEquals($value, $exception->getOriginalValue());
        $this->assertEquals('float', $exception->getTargetType());
        $this->assertStringContainsString('轉換為浮點數', $exception->getMessage());
    }

    public function testArrayConversionFailedException(): void
    {
        $key = 'headers';
        $value = '{invalid json}';
        $reason = 'JSON 語法錯誤';
        $exception = TypeConversionException::arrayConversionFailed($key, $value, $reason);

        $this->assertInstanceOf(TypeConversionException::class, $exception);
        $this->assertEquals(TypeConversionException::TYPE_ARRAY_CONVERSION_FAILED, $exception->getCode());
        $this->assertEquals($key, $exception->getConfigKey());
        $this->assertEquals($value, $exception->getOriginalValue());
        $this->assertEquals('array', $exception->getTargetType());
        $this->assertStringContainsString('轉換為陣列', $exception->getMessage());
        $this->assertStringContainsString($reason, $exception->getMessage());
    }

    public function testStringConversionFailedException(): void
    {
        $key = 'name';
        $value = ['complex', 'array'];
        $exception = TypeConversionException::stringConversionFailed($key, $value);

        $this->assertInstanceOf(TypeConversionException::class, $exception);
        $this->assertEquals(TypeConversionException::TYPE_STRING_CONVERSION_FAILED, $exception->getCode());
        $this->assertEquals($key, $exception->getConfigKey());
        $this->assertEquals($value, $exception->getOriginalValue());
        $this->assertEquals('string', $exception->getTargetType());
        $this->assertStringContainsString('轉換為字串', $exception->getMessage());
    }

    public function testUnsupportedTypeException(): void
    {
        $key = 'resource';
        $value = 'some-value';
        $type = 'resource';
        $exception = TypeConversionException::unsupportedType($key, $value, $type);

        $this->assertInstanceOf(TypeConversionException::class, $exception);
        $this->assertEquals(TypeConversionException::TYPE_UNSUPPORTED_TYPE, $exception->getCode());
        $this->assertEquals($key, $exception->getConfigKey());
        $this->assertEquals($value, $exception->getOriginalValue());
        $this->assertEquals($type, $exception->getTargetType());
        $this->assertStringContainsString('不支援的型別轉換', $exception->getMessage());
    }

    public function testGetErrorDescription(): void
    {
        $testCases = [
            [TypeConversionException::TYPE_BOOLEAN_CONVERSION_FAILED, '布林值轉換失敗'],
            [TypeConversionException::TYPE_INTEGER_CONVERSION_FAILED, '整數轉換失敗'],
            [TypeConversionException::TYPE_FLOAT_CONVERSION_FAILED, '浮點數轉換失敗'],
            [TypeConversionException::TYPE_ARRAY_CONVERSION_FAILED, '陣列轉換失敗'],
            [TypeConversionException::TYPE_STRING_CONVERSION_FAILED, '字串轉換失敗'],
            [TypeConversionException::TYPE_UNSUPPORTED_TYPE, '不支援的型別'],
        ];

        foreach ($testCases as [$code, $expectedDescription]) {
            $exception = new TypeConversionException(
                'Test message',
                $code,
                'test_key',
                'test_value',
                'test_type'
            );
            $this->assertEquals($expectedDescription, $exception->getErrorDescription());
        }
    }

    public function testGetRepairSuggestions(): void
    {
        $exception = TypeConversionException::booleanConversionFailed('enabled', 'maybe');
        $suggestions = $exception->getRepairSuggestions();

        $this->assertIsArray($suggestions);
        $this->assertNotEmpty($suggestions);
        $this->assertContains('使用有效的布林值：true, false, 1, 0, yes, no, on, off', $suggestions);
    }

    public function testGetDetailedReport(): void
    {
        $key = 'timeout';
        $value = 'invalid';
        $type = 'integer';
        $exception = TypeConversionException::integerConversionFailed($key, $value);
        $report = $exception->getDetailedReport();

        $this->assertIsString($report);
        $this->assertStringContainsString('型別轉換錯誤報告', $report);
        $this->assertStringContainsString('配置鍵', $report);
        $this->assertStringContainsString('原始值', $report);
        $this->assertStringContainsString('原始型別', $report);
        $this->assertStringContainsString('目標型別', $report);
        $this->assertStringContainsString('錯誤訊息', $report);
        $this->assertStringContainsString('修復建議', $report);
        $this->assertStringContainsString($key, $report);
        $this->assertStringContainsString($value, $report);
    }

    public function testConstructorParameters(): void
    {
        $message = 'Test conversion error';
        $code = TypeConversionException::TYPE_INTEGER_CONVERSION_FAILED;
        $key = 'test_key';
        $originalValue = 'test_value';
        $targetType = 'integer';

        $exception = new TypeConversionException(
            $message,
            $code,
            $key,
            $originalValue,
            $targetType
        );

        $this->assertEquals($message, $exception->getMessage());
        $this->assertEquals($code, $exception->getCode());
        $this->assertEquals($key, $exception->getConfigKey());
        $this->assertEquals($originalValue, $exception->getOriginalValue());
        $this->assertEquals($targetType, $exception->getTargetType());
    }

    public function testInheritanceFromConfigurationException(): void
    {
        $exception = TypeConversionException::booleanConversionFailed('test', 'value');

        $this->assertInstanceOf(\FDMC\FileServiceClient\Exceptions\ConfigurationException::class, $exception);
        $this->assertInstanceOf(\FDMC\FileServiceClient\Exceptions\FileServiceException::class, $exception);
        $this->assertInstanceOf(\Exception::class, $exception);
    }

    public function testComplexValueHandling(): void
    {
        // 測試複雜值的處理
        $complexValue = ['nested' => ['array' => 'value']];
        $exception = TypeConversionException::stringConversionFailed('complex', $complexValue);

        $this->assertEquals($complexValue, $exception->getOriginalValue());
        $this->assertStringContainsString('array', $exception->getMessage());
    }

    public function testNullValueHandling(): void
    {
        $exception = TypeConversionException::integerConversionFailed('nullable', null);

        $this->assertNull($exception->getOriginalValue());
        $this->assertStringContainsString('NULL', $exception->getMessage());
    }

    public function testBooleanValueHandling(): void
    {
        $exception = TypeConversionException::stringConversionFailed('boolean', true);

        $this->assertTrue($exception->getOriginalValue());
        $this->assertStringContainsString('true', $exception->getMessage());
    }
}