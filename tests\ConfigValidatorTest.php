<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\Processing\ConfigValidator;
use FDMC\FileServiceClient\Processing\ValidationResult;
use PHPUnit\Framework\TestCase;

class ConfigValidatorTest extends TestCase
{
    private ConfigValidator $validator;

    protected function setUp(): void
    {
        $this->validator = new ConfigValidator();
    }

    public function testValidateCompleteValidConfig(): void
    {
        $config = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => ['image/jpeg', 'image/png'],
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        $this->assertTrue($result->isValid());
        $this->assertFalse($result->hasErrors());
        $this->assertEmpty($result->getErrors());
        $this->assertEquals(0, $result->getErrorCount());
    }

    public function testValidateInvalidBaseUrl(): void
    {
        $config = [
            'baseUrl' => 'invalid-url',
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => ['image/jpeg'],
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        $this->assertFalse($result->isValid());
        $this->assertTrue($result->hasErrors());
        $this->assertEquals(1, $result->getErrorCount());

        $errors = $result->getErrorsForKey('baseUrl');
        $this->assertCount(1, $errors);
        $this->assertEquals('url', $errors[0]['type']);
        $this->assertStringContainsString('不是有效的 URL 格式', $errors[0]['message']);
    }

    public function testValidateMissingRequiredField(): void
    {
        $config = [
            // 缺少 baseUrl
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => ['image/jpeg'],
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        $this->assertFalse($result->isValid());
        $this->assertTrue($result->hasErrors());

        $errors = $result->getErrorsForKey('baseUrl');
        $this->assertCount(1, $errors);
        $this->assertEquals('required', $errors[0]['type']);
        $this->assertStringContainsString('為必填項目', $errors[0]['message']);
    }

    public function testValidateOutOfRangeValues(): void
    {
        $config = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 500, // 超出最大值 300
                'connect_timeout' => 0, // 小於最小值 1
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => ['image/jpeg'],
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        $this->assertFalse($result->isValid());
        $this->assertTrue($result->hasErrors());
        $this->assertEquals(2, $result->getErrorCount());

        // 檢查超出最大值的錯誤
        $timeoutErrors = $result->getErrorsForKey('http.timeout');
        $this->assertCount(1, $timeoutErrors);
        $this->assertEquals('max', $timeoutErrors[0]['type']);

        // 檢查小於最小值的錯誤
        $connectTimeoutErrors = $result->getErrorsForKey('http.connect_timeout');
        $this->assertCount(1, $connectTimeoutErrors);
        $this->assertEquals('min', $connectTimeoutErrors[0]['type']);
    }

    public function testValidateInvalidEnumValue(): void
    {
        $config = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => ['image/jpeg'],
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'invalid-driver', // 無效的驅動類型
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'invalid-level', // 無效的日誌等級
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        $this->assertFalse($result->isValid());
        $this->assertTrue($result->hasErrors());
        $this->assertEquals(2, $result->getErrorCount());

        // 檢查無效的快取驅動
        $cacheDriverErrors = $result->getErrorsForKey('cache.driver');
        $this->assertCount(1, $cacheDriverErrors);
        $this->assertEquals('enum', $cacheDriverErrors[0]['type']);

        // 檢查無效的日誌等級
        $logLevelErrors = $result->getErrorsForKey('logging.level');
        $this->assertCount(1, $logLevelErrors);
        $this->assertEquals('enum', $logLevelErrors[0]['type']);
    }

    public function testValidateInvalidTypeValues(): void
    {
        $config = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 'not-a-number', // 應該是整數
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => 'not-a-boolean', // 應該是布林值
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => 'not-an-array', // 應該是陣列
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        $this->assertFalse($result->isValid());
        $this->assertTrue($result->hasErrors());
        $this->assertEquals(3, $result->getErrorCount());

        // 檢查型別錯誤
        $typeErrors = $result->getErrorsByType('type');
        $this->assertCount(3, $typeErrors);
    }

    public function testValidateEmptyArray(): void
    {
        $config = [
            'baseUrl' => 'https://api.example.com',
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => [], // 空陣列
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        $this->assertFalse($result->isValid());
        $this->assertTrue($result->hasErrors());
        $this->assertEquals(1, $result->getErrorCount());

        $arrayErrors = $result->getErrorsForKey('upload.allowed_mime_types');
        $this->assertCount(1, $arrayErrors);
        $this->assertEquals('array_empty', $arrayErrors[0]['type']);
    }

    public function testGetValidationRules(): void
    {
        $rules = $this->validator->getValidationRules();

        $this->assertIsArray($rules);
        $this->assertArrayHasKey('baseUrl', $rules);
        $this->assertArrayHasKey('http.timeout', $rules);
        $this->assertArrayHasKey('logging.level', $rules);

        // 檢查規則結構
        $baseUrlRule = $rules['baseUrl'];
        $this->assertTrue($baseUrlRule['required']);
        $this->assertEquals('url', $baseUrlRule['type']);
        $this->assertArrayHasKey('description', $baseUrlRule);
    }

    public function testValidationResultMethods(): void
    {
        $config = [
            'baseUrl' => 'invalid-url',
            'http' => [
                'timeout' => 500, // 超出範圍
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'verify' => false,
                'http_errors' => false,
            ],
            'upload' => [
                'timeout' => 120,
                'max_file_size' => 104857600,
                'allowed_mime_types' => ['image/jpeg'],
            ],
            'download' => [
                'timeout' => 300,
                'stream_buffer_size' => 8192,
                'large_file_threshold' => 52428800,
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 3600,
            ],
            'logging' => [
                'enabled' => true,
                'level' => 'info',
                'channel' => 'default',
            ],
            'retry' => [
                'max_attempts' => 3,
                'delay' => 1000,
                'multiplier' => 2.0,
            ],
        ];

        $result = $this->validator->validate($config);

        // 測試基本方法
        $this->assertFalse($result->isValid());
        $this->assertTrue($result->hasErrors());
        $this->assertEquals(2, $result->getErrorCount());

        // 測試格式化錯誤訊息
        $formattedErrors = $result->getFormattedErrors();
        $this->assertCount(2, $formattedErrors);
        $this->assertIsString($formattedErrors[0]);

        // 測試摘要
        $summary = $result->getSummary();
        $this->assertStringContainsString('配置驗證失敗', $summary);
        $this->assertStringContainsString('2 個錯誤', $summary);

        // 測試修復建議
        $suggestions = $result->getRepairSuggestions();
        $this->assertIsArray($suggestions);

        // 測試詳細報告
        $report = $result->getDetailedReport();
        $this->assertStringContainsString('配置驗證報告', $report);

        // 測試轉換為陣列
        $array = $result->toArray();
        $this->assertIsArray($array);
        $this->assertArrayHasKey('is_valid', $array);
        $this->assertArrayHasKey('errors', $array);

        // 測試轉換為 JSON
        $json = $result->toJson();
        $this->assertIsString($json);
        $this->assertJson($json);
    }
}