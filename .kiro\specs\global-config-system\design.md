# 設計文件

## 概述

本設計文件描述了如何建立一個類似 Laravel 的全域配置系統，讓 File Service Client 套件能夠在 Laravel 和原生 PHP 專案中提供一致且靈活的配置管理體驗。系統將支援自動環境偵測、配置檔案發布、動態配置修改和環境變數覆蓋。

## 架構

### 整體架構圖

```mermaid
graph TB
    A[應用程式] --> B[全域 config 函數]
    B --> C{環境偵測}
    C -->|Laravel| D[Laravel Config Manager]
    C -->|原生 PHP| E[Global Config Manager]
    
    D --> F[Laravel 配置系統]
    E --> G[配置載入器]
    
    G --> H[配置檔案搜尋]
    H --> I[專案配置檔案]
    H --> J[套件預設配置]
    
    G --> K[環境變數處理器]
    K --> L[型別轉換器]
    
    F --> M[配置快取]
    G --> M
    
    subgraph "配置來源優先級"
        N[1. 動態設定] --> O[2. 使用者選項]
        O --> P[3. 環境變數]
        P --> Q[4. 專案配置檔案]
        Q --> R[5. 套件預設配置]
    end
```

### 核心元件架構

```mermaid
classDiagram
    class GlobalConfigManager {
        -static array $config
        -static array $runtimeConfig
        -static bool $isLaravel
        +static config(key, default)
        +static set(key, value)
        +static all()
        +static forget(key)
        +static detectEnvironment()
        +static loadConfig()
        +static mergeConfigs()
    }
    
    class ConfigLoader {
        +loadFromFile(path)
        +searchConfigFiles()
        +validateConfig(config)
        +mergeWithDefaults(config)
    }
    
    class EnvironmentProcessor {
        +processEnvVars(config)
        +convertType(value)
        +parseBoolean(value)
        +parseNumeric(value)
    }
    
    class LaravelServiceProvider {
        +register()
        +boot()
        +publishes()
        +mergeConfigFrom()
    }
    
    class ConfigPublisher {
        +publishToLaravel()
        +publishToNative()
        +copyConfigFile()
        +createConfigDirectory()
    }
    
    GlobalConfigManager --> ConfigLoader
    GlobalConfigManager --> EnvironmentProcessor
    LaravelServiceProvider --> GlobalConfigManager
    ConfigPublisher --> GlobalConfigManager
```

## 元件和介面

### 1. 全域配置管理器 (GlobalConfigManager)

**職責：** 提供統一的配置存取介面，自動偵測環境並選擇適當的配置載入策略。

**主要方法：**
- `config($key = null, $default = null)` - 獲取配置（即時載入）
- `set($key, $value)` - 動態設定配置（僅運行時）
- `forget($key)` - 移除運行時配置項目
- `isLaravel()` - 偵測 Laravel 環境
- `loadConfig()` - 載入配置（不使用快取）

**安全考量：**
- 避免全域函數命名衝突
- 提供命名空間版本作為主要介面：`file_service_config()`
- 全域 `config()` 函數作為可選功能，需明確啟用

### 2. 配置載入器 (ConfigLoader)

**職責：** 負責從各種來源載入配置檔案，包括搜尋、驗證和合併。

**主要方法：**
- `loadFromFile($path)` - 從指定路徑載入配置
- `getConfigSearchPaths()` - 獲取配置檔案搜尋路徑（改進的搜尋邏輯）
- `findProjectRoot()` - 尋找專案根目錄（基於 composer.json）
- `validateConfig($config)` - 驗證配置格式和必要欄位
- `mergeWithDefaults($config)` - 與預設配置合併

**改進的搜尋順序：**
1. 明確指定的自訂路徑（最高優先級）
2. 專案根目錄的 config/file-service.php
3. 當前工作目錄的 config/file-service.php
4. 相對於入口檔案的 config/file-service.php
5. 套件預設配置

### 3. 環境變數處理器 (EnvironmentProcessor)

**職責：** 處理環境變數的讀取、型別轉換和配置覆蓋。

**主要方法：**
- `processEnvironmentVariable($key, $value)` - 安全的型別轉換
- `parseBoolean($value)` - 嚴格的布林值解析
- `parseInteger($value)` - 整數解析與驗證
- `parseFloat($value)` - 浮點數解析與驗證
- `parseArray($value)` - JSON 陣列解析
- `getExpectedType($key)` - 獲取配置項目的預期型別

**安全改進：**
- 增加型別提示和驗證
- 使用 match 表達式進行型別轉換
- 完善的錯誤處理和回退機制
- 記錄型別轉換警告

### 4. Laravel 服務提供者 (LaravelServiceProvider)

**職責：** 在 Laravel 環境中註冊配置和提供發布功能。

**主要方法：**
- `register()` - 註冊服務和合併配置（僅限容器註冊）
- `boot()` - 發布配置和註冊全域函數（需要完整容器）
- `registerGlobalConfigFunction()` - 安全註冊全域函數
- `canRegisterGlobalFunction()` - 檢測函數衝突

**職責分離：**
- register(): 服務註冊、配置合併
- boot(): 資源發布、全域函數註冊（可選）

### 5. 配置驗證器 (ConfigValidator)

**職責：** 驗證配置檔案的格式和內容正確性。

**主要方法：**
- `validate($config)` - 驗證完整配置
- `validateRule($value, $rule)` - 驗證單一規則
- `getValidationRules()` - 獲取驗證規則定義

**驗證規則：**
- baseUrl: 必填且為有效 URL
- http.timeout: 整數，範圍 1-300
- upload.max_file_size: 正整數
- logging.level: 限定值 (debug,info,warning,error)

### 6. 配置發布器 (ConfigPublisher)

**職責：** 提供配置檔案發布功能，支援 Laravel 和原生 PHP 專案。

**主要方法：**
- `publishToLaravel()` - 發布到 Laravel 專案
- `publishToNative()` - 發布到原生 PHP 專案
- `copyConfigFile()` - 複製配置檔案
- `createConfigDirectory()` - 建立配置目錄

## 資料模型

### 配置結構

```php
[
    'baseUrl' => 'http://localhost:8080',
    'http' => [
        'timeout' => 30,
        'connect_timeout' => 10,
        'read_timeout' => 60,
        'verify' => false,
        'http_errors' => false,
    ],
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'User-Agent' => 'FDMC-FileServiceClient/1.0',
    ],
    'upload' => [
        'timeout' => 120,
        'max_file_size' => 104857600, // 100MB
        'allowed_mime_types' => [...],
    ],
    'download' => [
        'timeout' => 300,
        'stream_buffer_size' => 8192,
        'large_file_threshold' => 52428800, // 50MB
    ],
    'cache' => [
        'enabled' => false,
        'driver' => 'file',
        'ttl' => 3600,
    ],
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'channel' => 'default',
    ],
    'retry' => [
        'max_attempts' => 3,
        'delay' => 1000,
        'multiplier' => 2,
    ],
]
```

### 環境變數對應

```php
[
    'FILE_SERVICE_BASE_URL' => 'baseUrl',
    'FILE_SERVICE_TIMEOUT' => 'http.timeout',
    'FILE_SERVICE_CONNECT_TIMEOUT' => 'http.connect_timeout',
    'FILE_SERVICE_READ_TIMEOUT' => 'http.read_timeout',
    'FILE_SERVICE_SSL_VERIFY' => 'http.verify',
    'FILE_SERVICE_UPLOAD_TIMEOUT' => 'upload.timeout',
    'FILE_SERVICE_MAX_FILE_SIZE' => 'upload.max_file_size',
    'FILE_SERVICE_DOWNLOAD_TIMEOUT' => 'download.timeout',
    'FILE_SERVICE_CACHE_ENABLED' => 'cache.enabled',
    'FILE_SERVICE_CACHE_DRIVER' => 'cache.driver',
    'FILE_SERVICE_CACHE_TTL' => 'cache.ttl',
    'FILE_SERVICE_LOG_ENABLED' => 'logging.enabled',
    'FILE_SERVICE_LOG_LEVEL' => 'logging.level',
    'FILE_SERVICE_LOG_CHANNEL' => 'logging.channel',
    'FILE_SERVICE_RETRY_MAX_ATTEMPTS' => 'retry.max_attempts',
    'FILE_SERVICE_RETRY_DELAY' => 'retry.delay',
    'FILE_SERVICE_RETRY_MULTIPLIER' => 'retry.multiplier',
]
```

## 錯誤處理

### 錯誤類型和處理策略

1. **配置檔案不存在**
   - 策略：使用套件預設配置，記錄警告
   - 例外：不拋出例外，確保系統可用性

2. **配置檔案格式錯誤**
   - 策略：拋出 `ConfigurationException`
   - 訊息：提供詳細的格式錯誤說明

3. **環境變數型別轉換失敗**
   - 策略：使用原始字串值，記錄警告
   - 回退：使用配置檔案中的預設值

4. **配置鍵不存在**
   - 策略：返回提供的預設值
   - 行為：與 Laravel 的 `config()` 函數一致

5. **權限問題**
   - 策略：拋出 `ConfigurationException`
   - 訊息：提供權限問題的解決建議

### 例外類別設計

```php
class ConfigurationException extends FileServiceException
{
    public const CONFIG_FILE_NOT_READABLE = 1001;
    public const CONFIG_FORMAT_INVALID = 1002;
    public const CONFIG_PERMISSION_DENIED = 1003;
    public const CONFIG_VALIDATION_FAILED = 1004;
    public const CONFIG_TYPE_CONVERSION_FAILED = 1005;
    
    public static function fileNotReadable(string $path): self
    {
        return new self("配置檔案無法讀取: {$path}", self::CONFIG_FILE_NOT_READABLE);
    }
    
    public static function formatInvalid(string $path, string $reason): self
    {
        return new self("配置檔案格式錯誤 {$path}: {$reason}", self::CONFIG_FORMAT_INVALID);
    }
    
    public static function typeConversionFailed(string $key, string $value): self
    {
        return new self("環境變數 {$key} 型別轉換失敗: {$value}", self::CONFIG_TYPE_CONVERSION_FAILED);
    }
}
```

## 測試策略

### 單元測試

1. **GlobalConfigManager 測試**
   - 環境偵測功能
   - 配置載入和合併
   - 動態配置設定
   - 配置快取機制

2. **ConfigLoader 測試**
   - 配置檔案搜尋
   - 配置驗證
   - 錯誤處理

3. **EnvironmentProcessor 測試**
   - 環境變數讀取
   - 型別轉換
   - 優先級處理

### 整合測試

1. **Laravel 環境測試**
   - 服務提供者註冊
   - 配置發布功能
   - Laravel config 函數整合

2. **原生 PHP 環境測試**
   - 全域函數註冊
   - 配置檔案搜尋
   - 環境變數處理

### 功能測試

1. **配置優先級測試**
   - 驗證配置來源的優先級順序
   - 測試各種配置組合情況

2. **跨環境相容性測試**
   - Laravel 5.5+ 版本相容性
   - PHP 8.0+ 版本相容性
   - 不同作業系統相容性

### 效能測試

1. **配置載入效能**
   - 首次載入時間
   - 快取命中效能
   - 記憶體使用量

2. **大型配置檔案處理**
   - 處理複雜巢狀配置
   - 大量環境變數處理

## 實作細節

### 配置檔案搜尋順序

1. Laravel 環境：
   - `config('file-service')` (Laravel 配置系統)
   - `config_path('file-service.php')`
   - 套件預設配置

2. 原生 PHP 環境（改進的搜尋邏輯）：
   - 明確指定的自訂路徑（最高優先級）
   - 專案根目錄（基於 composer.json）的 config/file-service.php
   - 當前工作目錄的 config/file-service.php
   - 相對於入口檔案的 config/file-service.php
   - 套件預設配置

**搜尋改進：**
- 移除不安全的 `base_path()` 函數依賴
- 增加專案根目錄自動偵測
- 加入檔案權限和可讀性檢查
- 處理符號連結和相對路徑問題

### 環境變數處理邏輯（安全改進版）

```php
private function processEnvironmentVariable($key, $value): mixed
{
    // 增加型別提示和驗證
    $expectedType = $this->getExpectedType($key);
    
    try {
        return match($expectedType) {
            'boolean' => $this->parseBoolean($value),
            'integer' => $this->parseInteger($value),
            'float' => $this->parseFloat($value),
            'array' => $this->parseArray($value),
            'string' => $this->parseString($value),
            default => $value
        };
    } catch (TypeConversionException $e) {
        // 記錄警告並使用預設值
        $this->logWarning("環境變數 {$key} 型別轉換失敗: {$e->getMessage()}");
        return $this->getDefaultValue($key);
    }
}

private function parseBoolean($value): bool
{
    if (is_bool($value)) return $value;
    
    $normalized = strtolower(trim($value));
    if (in_array($normalized, ['true', '1', 'yes', 'on'], true)) {
        return true;
    }
    if (in_array($normalized, ['false', '0', 'no', 'off', ''], true)) {
        return false;
    }
    
    throw new TypeConversionException("無法將 '{$value}' 轉換為布林值");
}

private function parseInteger($value): int
{
    if (is_int($value)) return $value;
    
    if (!is_numeric($value) || strpos($value, '.') !== false) {
        throw new TypeConversionException("無法將 '{$value}' 轉換為整數");
    }
    
    return (int) $value;
}
```

### 配置載入策略（移除快取機制）

**即時載入策略：**
- 每次存取都重新載入配置，確保配置是最新的
- 僅保留運行時配置的記憶體存儲
- 避免長時間運行應用的記憶體洩漏問題
- 簡化配置更新和偵錯流程

**運行時配置管理：**
```php
class GlobalConfigManager
{
    private static array $runtimeConfig = []; // 僅保留運行時配置
    private static bool $isLaravel;
    
    public static function config($key = null, $default = null)
    {
        // 每次都重新載入配置，確保配置是最新的
        $config = self::loadConfig();
        
        // 合併運行時配置
        $config = array_merge($config, self::$runtimeConfig);
        
        if ($key === null) {
            return $config;
        }
        
        return data_get($config, $key, $default);
    }
    
    public static function set($key, $value): void
    {
        // 只存儲運行時修改的配置
        data_set(self::$runtimeConfig, $key, $value);
    }
}
```

### Laravel 服務提供者實作（改進版）

```php
class FileServiceConfigServiceProvider extends ServiceProvider
{
    public function register()
    {
        // 只在這裡註冊服務和合併配置
        $this->mergeConfigFrom(
            __DIR__ . '/../config/file-service.php',
            'file-service'
        );
        
        $this->app->singleton(GlobalConfigManager::class);
    }
    
    public function boot()
    {
        // 只在這裡處理需要容器完全載入後的操作
        $this->publishes([
            __DIR__ . '/../config/file-service.php' => config_path('file-service.php'),
        ], 'file-service-config');
        
        // 安全註冊全域函數（可選功能）
        if (config('file-service.enable_global_config_function', false)) {
            $this->registerGlobalConfigFunction();
        }
    }
    
    private function registerGlobalConfigFunction(): void
    {
        if ($this->canRegisterGlobalFunction()) {
            require_once __DIR__ . '/helpers.php';
        } else {
            $this->app['log']->warning('無法註冊全域 config 函數：函數已存在');
        }
    }
    
    private function canRegisterGlobalFunction(): bool
    {
        if (!function_exists('config')) {
            return true;
        }
        
        try {
            $reflection = new ReflectionFunction('config');
            // 檢查是否為 Laravel 內建函數
            return str_contains($reflection->getFileName(), 'laravel');
        } catch (ReflectionException $e) {
            return false;
        }
    }
}
```

**全域函數安全實作：**
```php
// helpers.php
if (!function_exists('file_service_config')) {
    function file_service_config($key = null, $default = null)
    {
        return \FDMC\FileServiceClient\GlobalConfigManager::config($key, $default);
    }
}

// 可選的全域函數（需明確啟用）
if (!function_exists('config') && config('file-service.enable_global_config_function', false)) {
    function config($key = null, $default = null)
    {
        return file_service_config($key, $default);
    }
}
```

### 向後相容性

1. **現有 ConfigManager 相容**
   - 保持現有 API 不變
   - 新功能作為額外方法提供
   - 逐步遷移策略

2. **配置格式相容**
   - 支援現有配置檔案格式
   - 新增功能使用可選配置項目

3. **環境變數相容**
   - 保持現有環境變數名稱
   - 新增環境變數使用一致的命名規範