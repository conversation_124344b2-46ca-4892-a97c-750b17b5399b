<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../src/helpers.php';

use FDMC\FileServiceClient\GlobalConfigManager;

echo "=== File Service Client 配置合併範例 ===\n\n";

// 1. 基本配置獲取
echo "1. 基本配置獲取:\n";
$baseUrl = file_service_config('baseUrl');
$timeout = file_service_config('http.timeout');
echo "   Base URL: {$baseUrl}\n";
echo "   HTTP Timeout: {$timeout}\n\n";

// 2. 環境變數覆蓋示範
echo "2. 環境變數覆蓋示範:\n";
echo "   設定環境變數...\n";
$_ENV['FILE_SERVICE_BASE_URL'] = 'https://env.example.com';
$_ENV['FILE_SERVICE_TIMEOUT'] = '45';
$_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';

// 重新載入配置以應用環境變數
GlobalConfigManager::resetEnvironmentDetection();
$config = file_service_config();

echo "   環境變數覆蓋後:\n";
echo "   - Base URL: {$config['baseUrl']}\n";
echo "   - HTTP Timeout: {$config['http']['timeout']}\n";
echo "   - SSL Verify: " . ($config['http']['verify'] ? 'true' : 'false') . "\n\n";

// 3. 運行時配置修改
echo "3. 運行時配置修改:\n";
echo "   動態修改配置...\n";
file_service_config('baseUrl', 'https://runtime.example.com');
file_service_config('http.connect_timeout', 20);

$config = file_service_config();
echo "   運行時修改後:\n";
echo "   - Base URL: {$config['baseUrl']}\n";
echo "   - Connect Timeout: {$config['http']['connect_timeout']}\n\n";

// 4. 批量配置設定
echo "4. 批量配置設定:\n";
echo "   批量設定配置...\n";
file_service_config([
    'upload.max_file_size' => 209715200, // 200MB
    'headers.Authorization' => 'Bearer token123',
    'custom.feature_flag' => true,
]);

$config = file_service_config();
echo "   批量設定後:\n";
echo "   - Max File Size: " . number_format($config['upload']['max_file_size'] / 1024 / 1024, 0) . "MB\n";
echo "   - Authorization Header: {$config['headers']['Authorization']}\n";
echo "   - Custom Feature Flag: " . ($config['custom']['feature_flag'] ? 'enabled' : 'disabled') . "\n\n";

// 5. 配置優先級展示
echo "5. 配置優先級展示:\n";
echo "   配置來源優先級（高到低）:\n";
echo "   1. 運行時配置（動態設定）\n";
echo "   2. 使用者選項\n";
echo "   3. 環境變數\n";
echo "   4. 專案配置檔案\n";
echo "   5. 套件預設配置\n\n";

echo "   當前 baseUrl 的來源追蹤:\n";
echo "   - 預設值: http://localhost:8080\n";
echo "   - 環境變數: https://env.example.com\n";
echo "   - 運行時設定: https://runtime.example.com\n";
echo "   - 最終值: {$config['baseUrl']} (運行時設定優先)\n\n";

// 6. 巢狀配置合併
echo "6. 巢狀配置合併:\n";
echo "   展示巢狀配置如何正確合併...\n";

// 顯示 HTTP 配置的合併結果
echo "   HTTP 配置合併結果:\n";
foreach ($config['http'] as $key => $value) {
    $valueStr = is_bool($value) ? ($value ? 'true' : 'false') : $value;
    echo "   - {$key}: {$valueStr}\n";
}
echo "\n";

// 7. 配置驗證
echo "7. 配置驗證:\n";
echo "   檢查必要配置項目...\n";
$requiredKeys = ['baseUrl', 'http.timeout', 'headers.Content-Type'];
foreach ($requiredKeys as $key) {
    $exists = file_service_config_has($key);
    $status = $exists ? '✓' : '✗';
    echo "   {$status} {$key}\n";
}
echo "\n";

// 8. 無效環境變數處理
echo "8. 無效環境變數處理:\n";
echo "   設定無效環境變數...\n";
$_ENV['FILE_SERVICE_TIMEOUT'] = 'invalid_number';
$_ENV['FILE_SERVICE_SSL_VERIFY'] = 'invalid_boolean';

// 重新載入配置
GlobalConfigManager::resetEnvironmentDetection();
GlobalConfigManager::clearRuntimeConfig();
$config = file_service_config();

echo "   無效環境變數處理結果:\n";
echo "   - Timeout: {$config['http']['timeout']} (使用預設值，忽略無效環境變數)\n";
echo "   - SSL Verify: " . ($config['http']['verify'] ? 'true' : 'false') . " (使用預設值)\n\n";

// 9. 配置重置
echo "9. 配置重置:\n";
echo "   清除運行時配置...\n";
GlobalConfigManager::clearRuntimeConfig();
$config = file_service_config();

echo "   重置後的 baseUrl: {$config['baseUrl']}\n";
echo "   (回到環境變數值或預設值)\n\n";

// 10. 完整配置展示
echo "10. 完整配置展示:\n";
echo "    當前所有配置:\n";
$allConfig = file_service_config_all();
echo json_encode($allConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== 配置合併範例完成 ===\n";