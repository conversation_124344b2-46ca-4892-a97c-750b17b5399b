<?php

/**
 * 配置發布和自訂範例
 * 
 * 展示如何發布、自訂和管理 File Service Client 的配置檔案
 */

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\ConfigPublisher;
use FDMC\FileServiceClient\ConfigLoader;
use FDMC\FileServiceClient\ConfigValidator;
use FDMC\FileServiceClient\GlobalConfigManager;
use FDMC\FileServiceClient\FileServiceClient;

// 載入全域配置函數
require_once __DIR__ . '/../src/helpers.php';

// 模擬 env 函數
if (!function_exists('env')) {
    function env($key, $default = null) {
        $value = $_ENV[$key] ?? getenv($key);
        if ($value === false) {
            return $default;
        }
        return $value;
    }
}

echo "=== 配置發布和自訂範例 ===\n\n";

// 設定範例目錄
$exampleDir = __DIR__ . '/../config-publish-example';
$configDir = $exampleDir . '/config';

// 清理並創建目錄
if (is_dir($exampleDir)) {
    $files = glob($exampleDir . '/*');
    foreach ($files as $file) {
        if (is_file($file)) unlink($file);
        if (is_dir($file)) {
            $subFiles = glob($file . '/*');
            foreach ($subFiles as $subFile) unlink($subFile);
            rmdir($file);
        }
    }
    rmdir($exampleDir);
}

mkdir($exampleDir, 0755, true);
mkdir($configDir, 0755, true);

echo "1. 配置檔案發布\n";

try {
    $publisher = new ConfigPublisher();
    $sourceConfig = __DIR__ . '/../config/file-service.php';
    $targetConfig = $configDir . '/file-service.php';
    
    if (file_exists($sourceConfig)) {
        // 發布配置檔案
        copy($sourceConfig, $targetConfig);
        echo "✓ 配置檔案已發布到: {$targetConfig}\n";
        
        // 驗證發布的檔案
        if (file_exists($targetConfig) && is_readable($targetConfig)) {
            echo "✓ 發布的配置檔案可以正常讀取\n";
            $publishedConfig = require $targetConfig;
            echo "✓ 發布的配置包含 " . count($publishedConfig) . " 個頂層項目\n";
        }
    } else {
        echo "⚠ 未找到源配置檔案，創建基本配置檔案\n";
        
        // 創建基本配置檔案
        $basicConfig = [
            'baseUrl' => env('FILE_SERVICE_BASE_URL', 'http://localhost:8080'),
            'http' => [
                'timeout' => (int) env('FILE_SERVICE_TIMEOUT', 30),
                'connect_timeout' => 10,
                'verify' => false,
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
        ];
        
        $configContent = "<?php\n\nreturn " . var_export($basicConfig, true) . ";\n";
        file_put_contents($targetConfig, $configContent);
        echo "✓ 基本配置檔案已創建\n";
    }
    
} catch (Exception $e) {
    echo "✗ 配置發布失敗: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. 配置檔案自訂
echo "2. 配置檔案自訂\n";

$customConfigPath = $configDir . '/file-service-custom.php';

echo "創建自訂配置檔案...\n";

$customConfig = [
    'baseUrl' => 'https://custom-api.example.com',
    
    'http' => [
        'timeout' => 45,
        'connect_timeout' => 15,
        'read_timeout' => 90,
        'verify' => true,
        'http_errors' => false,
    ],
    
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'User-Agent' => 'CustomApp/2.0',
        'X-API-Version' => 'v2',
        'Authorization' => env('API_AUTHORIZATION', 'Bearer default-token'),
    ],
    
    'upload' => [
        'timeout' => 300,
        'max_file_size' => 200 * 1024 * 1024, // 200MB
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'text/plain',
            'application/json',
            'application/zip',
            'video/mp4',
            'audio/mpeg',
        ],
        'chunk_size' => 1024 * 1024, // 1MB chunks
    ],
    
    'download' => [
        'timeout' => 600,
        'stream_buffer_size' => 16384, // 16KB
        'large_file_threshold' => 100 * 1024 * 1024, // 100MB
        'retry_on_failure' => true,
    ],
    
    'cache' => [
        'enabled' => true,
        'driver' => 'file',
        'ttl' => 7200, // 2 hours
        'prefix' => 'fs_',
        'path' => sys_get_temp_dir() . '/file-service-cache',
    ],
    
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'channel' => 'file-service',
        'path' => __DIR__ . '/../logs/file-service.log',
        'max_files' => 7,
    ],
    
    'retry' => [
        'max_attempts' => 5,
        'delay' => 2000, // 2 seconds
        'multiplier' => 2,
        'max_delay' => 30000, // 30 seconds
    ],
    
    'security' => [
        'api_key_header' => 'X-API-Key',
        'rate_limit' => [
            'enabled' => true,
            'max_requests' => 1000,
            'window_minutes' => 60,
        ],
    ],
    
    'features' => [
        'async_upload' => true,
        'progress_tracking' => true,
        'thumbnail_generation' => false,
        'virus_scanning' => true,
    ],
];

$customConfigContent = "<?php\n\n/**\n * File Service Client 自訂配置\n * \n * 此檔案包含針對特定應用需求的自訂設定\n */\n\nreturn " . var_export($customConfig, true) . ";\n";

file_put_contents($customConfigPath, $customConfigContent);
echo "✓ 自訂配置檔案已創建: {$customConfigPath}\n";

// 驗證自訂配置
$validator = new ConfigValidator();
$loadedCustomConfig = require $customConfigPath;
$validationResult = $validator->validate($loadedCustomConfig);

if ($validationResult->isValid()) {
    echo "✓ 自訂配置檔案驗證通過\n";
} else {
    echo "⚠ 自訂配置檔案驗證發現問題:\n";
    foreach ($validationResult->getErrors() as $error) {
        echo "  - {$error}\n";
    }
}

echo "\n";

// 3. 配置載入優先級測試
echo "3. 配置載入優先級測試\n";

// 測試不同配置來源的優先級
echo "測試配置載入優先級...\n";

// 設定環境變數
$_ENV['FILE_SERVICE_BASE_URL'] = 'https://env.example.com';
$_ENV['FILE_SERVICE_TIMEOUT'] = '25';

echo "設定環境變數:\n";
echo "- FILE_SERVICE_BASE_URL=https://env.example.com\n";
echo "- FILE_SERVICE_TIMEOUT=25\n";

// 載入配置（使用自訂配置檔案）
$loader = new ConfigLoader();
$loader->setCustomPath($customConfigPath);
$configFromFile = $loader->loadFromFile();

echo "\n從自訂配置檔案載入的配置:\n";
echo "- Base URL: " . $configFromFile['baseUrl'] . "\n";
echo "- Timeout: " . $configFromFile['http']['timeout'] . " 秒\n";

// 測試全域配置管理器
$globalConfig = GlobalConfigManager::loadConfig();
echo "\n全域配置管理器載入的配置:\n";
echo "- Base URL: " . $globalConfig['baseUrl'] . "\n";
echo "- Timeout: " . $globalConfig['http']['timeout'] . " 秒\n";

echo "\n";

// 4. 運行時配置修改
echo "4. 運行時配置修改\n";

echo "使用自訂配置創建客戶端...\n";
$client = new FileServiceClient($loadedCustomConfig);
echo "✓ 客戶端已創建，Base URL: " . $client->getBaseUrl() . "\n";

echo "\n運行時修改配置:\n";
GlobalConfigManager::set([
    'runtime.environment' => 'development',
    'runtime.debug' => true,
    'runtime.request_id' => uniqid('req_', true),
]);

echo "- Environment: " . GlobalConfigManager::config('runtime.environment') . "\n";
echo "- Debug: " . (GlobalConfigManager::config('runtime.debug') ? 'true' : 'false') . "\n";
echo "- Request ID: " . GlobalConfigManager::config('runtime.request_id') . "\n";

echo "\n";

// 5. 配置備份和還原
echo "5. 配置備份和還原\n";

$backupDir = $exampleDir . '/backup';
mkdir($backupDir, 0755, true);

$backupPath = $backupDir . '/file-service-backup-' . date('Y-m-d-H-i-s') . '.php';

echo "備份當前配置到: {$backupPath}\n";
$currentConfig = GlobalConfigManager::all();
$backupContent = "<?php\n\n/**\n * File Service Client 配置備份\n * 建立時間: " . date('Y-m-d H:i:s') . "\n */\n\nreturn " . var_export($currentConfig, true) . ";\n";

file_put_contents($backupPath, $backupContent);
echo "✓ 配置已備份\n";

// 模擬配置還原
echo "\n模擬從備份還原配置:\n";
$restoredConfig = require $backupPath;
echo "✓ 從備份載入了 " . count($restoredConfig) . " 個配置項目\n";

echo "\n";

// 6. 配置模板生成
echo "6. 配置模板生成\n";

$templatePath = $exampleDir . '/file-service-template.php';

echo "生成配置模板...\n";

$template = "<?php

/**
 * File Service Client 配置模板
 * 
 * 複製此檔案並根據您的需求修改配置值
 */

return [
    // 基本服務配置
    'baseUrl' => env('FILE_SERVICE_BASE_URL', 'http://localhost:8080'),
    
    // HTTP 客戶端配置
    'http' => [
        'timeout' => (int) env('FILE_SERVICE_TIMEOUT', 30),
        'connect_timeout' => (int) env('FILE_SERVICE_CONNECT_TIMEOUT', 10),
        'read_timeout' => (int) env('FILE_SERVICE_READ_TIMEOUT', 60),
        'verify' => (bool) env('FILE_SERVICE_SSL_VERIFY', false),
        'http_errors' => false,
    ],
    
    // HTTP 標頭配置
    'headers' => [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'User-Agent' => 'FileServiceClient/1.0',
        // 添加您的自訂標頭
        // 'Authorization' => 'Bearer your-token',
        // 'X-API-Key' => 'your-api-key',
    ],
    
    // 檔案上傳配置
    'upload' => [
        'timeout' => (int) env('FILE_SERVICE_UPLOAD_TIMEOUT', 120),
        'max_file_size' => (int) env('FILE_SERVICE_MAX_FILE_SIZE', 104857600), // 100MB
        'allowed_mime_types' => [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'text/plain', 'application/json',
            // 根據需要添加更多類型
        ],
    ],
    
    // 檔案下載配置
    'download' => [
        'timeout' => (int) env('FILE_SERVICE_DOWNLOAD_TIMEOUT', 300),
        'stream_buffer_size' => 8192,
        'large_file_threshold' => 52428800, // 50MB
    ],
    
    // 快取配置
    'cache' => [
        'enabled' => (bool) env('FILE_SERVICE_CACHE_ENABLED', false),
        'driver' => env('FILE_SERVICE_CACHE_DRIVER', 'file'),
        'ttl' => (int) env('FILE_SERVICE_CACHE_TTL', 3600),
    ],
    
    // 日誌配置
    'logging' => [
        'enabled' => (bool) env('FILE_SERVICE_LOG_ENABLED', true),
        'level' => env('FILE_SERVICE_LOG_LEVEL', 'info'),
        'channel' => env('FILE_SERVICE_LOG_CHANNEL', 'default'),
    ],
    
    // 重試配置
    'retry' => [
        'max_attempts' => (int) env('FILE_SERVICE_RETRY_MAX_ATTEMPTS', 3),
        'delay' => (int) env('FILE_SERVICE_RETRY_DELAY', 1000),
        'multiplier' => (int) env('FILE_SERVICE_RETRY_MULTIPLIER', 2),
    ],
    
    // 在此添加您的自訂配置...
];
";

file_put_contents($templatePath, $template);
echo "✓ 配置模板已生成: {$templatePath}\n";

echo "\n";

// 7. 配置差異比較
echo "7. 配置差異比較\n";

$defaultConfig = require __DIR__ . '/../config/file-service.php';
$differences = [];

function compareConfigs($default, $custom, $path = '') {
    $diff = [];
    
    foreach ($custom as $key => $value) {
        $currentPath = $path ? $path . '.' . $key : $key;
        
        if (!array_key_exists($key, $default)) {
            $diff[] = "新增: {$currentPath} = " . (is_array($value) ? '[array]' : $value);
        } elseif (is_array($value) && is_array($default[$key])) {
            $subDiff = compareConfigs($default[$key], $value, $currentPath);
            $diff = array_merge($diff, $subDiff);
        } elseif ($value !== $default[$key]) {
            $oldValue = is_array($default[$key]) ? '[array]' : $default[$key];
            $newValue = is_array($value) ? '[array]' : $value;
            $diff[] = "修改: {$currentPath} = {$newValue} (原: {$oldValue})";
        }
    }
    
    return $diff;
}

$differences = compareConfigs($defaultConfig, $loadedCustomConfig);

echo "自訂配置與預設配置的差異:\n";
if (empty($differences)) {
    echo "沒有發現差異\n";
} else {
    foreach (array_slice($differences, 0, 10) as $diff) { // 只顯示前10個差異
        echo "- {$diff}\n";
    }
    if (count($differences) > 10) {
        echo "... 還有 " . (count($differences) - 10) . " 個差異\n";
    }
}

echo "\n";

// 8. 清理
echo "8. 清理\n";

echo "清理範例檔案...\n";

// 遞迴刪除目錄
function deleteDirectory($dir) {
    if (!is_dir($dir)) return;
    
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        is_dir($path) ? deleteDirectory($path) : unlink($path);
    }
    rmdir($dir);
}

deleteDirectory($exampleDir);
echo "✓ 範例檔案已清理\n";

// 清理環境變數
unset($_ENV['FILE_SERVICE_BASE_URL']);
unset($_ENV['FILE_SERVICE_TIMEOUT']);

echo "✓ 環境變數已清理\n";

// 清理運行時配置
GlobalConfigManager::clearRuntimeConfig();
echo "✓ 運行時配置已清理\n";

echo "\n=== 配置發布和自訂範例結束 ===\n";

echo "\n配置管理最佳實踐:\n";
echo "1. 使用版本控制管理配置檔案\n";
echo "2. 敏感資訊使用環境變數\n";
echo "3. 為不同環境準備不同的配置\n";
echo "4. 定期備份重要配置\n";
echo "5. 使用配置模板簡化設定\n";
echo "6. 驗證配置檔案的正確性\n";
echo "7. 記錄配置變更歷史\n";
