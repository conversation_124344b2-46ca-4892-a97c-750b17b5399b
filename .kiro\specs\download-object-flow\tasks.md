# 實作計劃

- [x] 1. 優化 FileServiceClient 的 downloadObject 方法
  - 修改現有的 downloadObject 方法，根據 storage.type 來判斷下載策略
  - 加強儲存類型的判斷邏輯，支援 AP_LOCAL、FS_LOCAL 等類型
  - 改善錯誤處理機制，提供更明確的錯誤訊息
  - _需求: 1.1, 1.2, 1.3, 2.1, 2.2, 2.4_

- [] 2. 實作 file-service 的物件資訊 API 優化
  - 確保 `/api/objects/{objectId}/metadata` 端點回傳包含儲存類型的完整資訊
  - 實作業務邏輯來判斷物件對應的儲存類型
  - 建立 DTO 結構來組合下載所需的資訊
  - _需求: 4.1, 4.2, 5.1, 5.2_

- [ ] 3. 實作 file-service 的下載 API 處理邏輯
  - 優化 `/api/objects/{objectId}/download` 端點的實作
  - 根據物件的儲存資訊定位實際檔案位置
  - 實作不同儲存類型的檔案存取邏輯
  - 確保以串流方式回傳檔案內容
  - _需求: 4.1, 4.3, 4.4, 2.1, 2.2, 2.3_

- [ ] 4. 實作 Storage Service 的路徑解析功能
  - 建立根據儲存類型解析檔案實際位置的服務方法
  - 實作路徑安全性驗證，防止路徑遍歷攻擊
  - 支援 FS_LOCAL、AP_LOCAL 等不同儲存類型的路徑處理
  - _需求: 2.1, 2.2, 2.3, 5.3_

- [ ] 5. 實作串流處理優化
  - 確保 FSC 與 FS 之間的檔案傳輸使用串流方式
  - 優化大檔案處理，避免記憶體溢出問題
  - 實作串流錯誤處理和資源清理機制
  - 加入檔案完整性驗證
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 6. 建立 Object Service 的下載資訊整合功能
  - 實作物件資訊查詢功能，整合儲存類型判斷邏輯
  - 建立專用的 ObjectDownloadInfo DTO 結構
  - 實作二次轉換邏輯，組合下載所需的完整資訊
  - _需求: 5.1, 5.2, 5.3_

- [ ] 7. 加強錯誤處理和驗證機制
  - 實作 PHP 端的自定義例外類別（FileNotFoundException、StorageTypeException 等）
  - 實作 Go 端的錯誤定義和處理邏輯
  - 建立完整的錯誤處理流程，涵蓋各種異常情況
  - _需求: 2.4, 4.4_

- [ ] 8. 撰寫單元測試
  - 為 FileServiceClient 的 downloadObject 方法撰寫測試
  - 測試不同儲存類型的下載流程
  - 測試錯誤處理和邊界情況
  - 為 file-service 的相關 API 撰寫測試
  - _需求: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 3.1, 3.2, 4.1, 4.2, 4.3_

- [ ] 9. 撰寫整合測試
  - 建立端到端的下載流程測試
  - 測試 FSC 與 FS 之間的協調機制
  - 驗證不同儲存類型的完整下載流程
  - 測試串流處理的正確性
  - _需求: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 3.4, 4.1, 4.2, 4.3, 4.4_

- [ ] 10. 效能測試和優化
  - 進行大檔案下載測試（>100MB）
  - 測試並發下載的效能表現
  - 監控記憶體使用量，確保串流處理的效率
  - 根據測試結果進行效能優化
  - _需求: 3.1, 3.2, 3.3, 3.4_