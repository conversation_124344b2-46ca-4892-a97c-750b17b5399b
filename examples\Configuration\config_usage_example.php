<?php

require_once __DIR__ . '/../vendor/autoload.php';

use FDMC\FileServiceClient\FileServiceClient;
use FDMC\FileServiceClient\ConfigManager;

echo "=== File Service Client 配置系統使用範例 ===\n\n";

try {
    // 範例 1: 使用預設配置
    echo "1. 使用預設配置:\n";
    $client1 = new FileServiceClient();
    echo "   基礎 URL: " . $client1->getBaseUrl() . "\n\n";

    // 範例 2: 使用自定義選項覆蓋配置
    echo "2. 使用自定義選項覆蓋配置:\n";
    $customOptions = [
        'baseUrl' => 'https://api.example.com',
        'http' => [
            'timeout' => 60,
        ],
        'headers' => [
            'Authorization' => 'Bearer your-token-here',
        ],
    ];
    
    $client2 = new FileServiceClient($customOptions);
    echo "   基礎 URL: " . $client2->getBaseUrl() . "\n\n";

    // 範例 3: 直接使用 ConfigManager 獲取配置
    echo "3. 直接使用 ConfigManager 獲取配置:\n";
    
    // 獲取所有配置
    $allConfig = ConfigManager::all();
    echo "   預設基礎 URL: " . $allConfig['baseUrl'] . "\n";
    
    // 獲取特定配置值
    $timeout = ConfigManager::get('http.timeout');
    echo "   預設超時時間: {$timeout} 秒\n";
    
    // 獲取上傳配置
    $uploadConfig = ConfigManager::getUploadConfig();
    echo "   最大檔案大小: " . number_format($uploadConfig['max_file_size'] / 1024 / 1024, 2) . " MB\n";
    
    // 獲取下載配置
    $downloadConfig = ConfigManager::getDownloadConfig();
    echo "   下載超時時間: {$downloadConfig['timeout']} 秒\n\n";

    // 範例 4: 合併使用者選項與預設配置
    echo "4. 合併使用者選項與預設配置:\n";
    $userOptions = [
        'baseUrl' => 'https://custom.api.com',
        'http' => [
            'timeout' => 45, // 只覆蓋 timeout，其他保持預設
        ],
        'headers' => [
            'X-Custom-Header' => 'custom-value', // 新增自定義標頭
        ],
    ];
    
    $mergedConfig = ConfigManager::mergeWithUserOptions($userOptions);
    echo "   合併後的基礎 URL: " . $mergedConfig['baseUrl'] . "\n";
    echo "   合併後的超時時間: " . $mergedConfig['http']['timeout'] . " 秒\n";
    echo "   合併後的連接超時: " . $mergedConfig['http']['connect_timeout'] . " 秒 (保持預設)\n";
    echo "   自定義標頭: " . $mergedConfig['headers']['X-Custom-Header'] . "\n";
    echo "   預設標頭仍存在: " . $mergedConfig['headers']['Content-Type'] . "\n\n";

    // 範例 5: 環境變數覆蓋
    echo "5. 環境變數覆蓋範例:\n";
    echo "   設置環境變數: FILE_SERVICE_BASE_URL=https://env.api.com\n";
    putenv('FILE_SERVICE_BASE_URL=https://env.api.com');
    
    // 清除配置快取以重新載入
    ConfigManager::clearCache();
    
    $envConfig = ConfigManager::get('baseUrl');
    echo "   從環境變數獲取的 URL: {$envConfig}\n\n";

    // 範例 6: 配置優先級展示
    echo "6. 配置優先級展示 (使用者選項 > 環境變數 > 配置檔案):\n";
    
    // 設置環境變數
    putenv('FILE_SERVICE_TIMEOUT=99');
    ConfigManager::clearCache();
    
    // 使用者選項會覆蓋環境變數
    $priorityOptions = [
        'http' => [
            'timeout' => 88, // 這個會覆蓋環境變數的 99
        ],
    ];
    
    $priorityConfig = ConfigManager::mergeWithUserOptions($priorityOptions);
    echo "   環境變數設置 timeout=99，但使用者選項設置 timeout=88\n";
    echo "   最終結果: " . $priorityConfig['http']['timeout'] . " 秒 (使用者選項優先)\n\n";

    echo "=== 配置系統展示完成 ===\n";

} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
    echo "檔案: " . $e->getFile() . ":" . $e->getLine() . "\n";
}